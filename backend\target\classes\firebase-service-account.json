{"type": "service_account", "project_id": "grocease-app-demo", "private_key_id": "demo-key-id", "private_key": "-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC7VJTUt9Us8cKB\nxhXBaaxLHP8gDVQcBiGjZR4WOhiHXgBrUcOSBTTSNIHlxQoTlVKFXTIUewHDnLwM\nllkT4ufn9wNiw6C4hn+GDjx98jjmpER4HFpVyuXiNjMO9cJVaoGWmxzxkxeQBHNV\nXLFXOlhgdVfuL0ps2RKctqEdCGSa+ktMO5cwtRbdUsaLrPymu1Dkzdjv1ZUMMHQK\nMQrBC1E/BVtyg+8LwmJmjGTXp2NNvsixjCL9Y/LrTdwRxXTN2A8=\n-----END PRIVATE KEY-----\n", "client_email": "*******", "client_id": "123456789012345678901", "auth_uri": "https://accounts.google.com/o/oauth2/auth", "token_uri": "https://oauth2.googleapis.com/token", "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs", "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-demo%40grocease-app-demo.iam.gserviceaccount.com", "universe_domain": "googleapis.com"}