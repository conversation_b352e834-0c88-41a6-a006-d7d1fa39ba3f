package com.grocease.service;

import com.cloudinary.Cloudinary;
import com.cloudinary.utils.ObjectUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Map;

@Service
@Slf4j
public class CloudinaryService {

    private final Cloudinary cloudinary;

    public CloudinaryService(@Autowired(required = false) Cloudinary cloudinary) {
        this.cloudinary = cloudinary;
    }

    public String uploadImage(MultipartFile file, String folder) throws IOException {
        if (cloudinary == null) {
            log.warn("Cloudinary not configured. Returning placeholder URL for file: {}", file.getOriginalFilename());
            return "https://via.placeholder.com/400x400?text=" + file.getOriginalFilename();
        }

        log.info("Uploading image to Cloudinary - folder: {}, filename: {}", folder, file.getOriginalFilename());

        Map<String, Object> uploadParams = ObjectUtils.asMap(
                "folder", folder,
                "resource_type", "image",
                "quality", "auto",
                "fetch_format", "auto"
        );

        Map<String, Object> uploadResult = cloudinary.uploader().upload(file.getBytes(), uploadParams);
        String imageUrl = (String) uploadResult.get("secure_url");

        log.info("Image uploaded successfully: {}", imageUrl);
        return imageUrl;
    }

    public String uploadUserAvatar(MultipartFile file) throws IOException {
        return uploadImage(file, "grocease/avatars");
    }

    public String uploadProductImage(MultipartFile file) throws IOException {
        return uploadImage(file, "grocease/products");
    }

    public String uploadCategoryImage(MultipartFile file) throws IOException {
        return uploadImage(file, "grocease/categories");
    }

    public String uploadBannerImage(MultipartFile file) throws IOException {
        return uploadImage(file, "grocease/banners");
    }

    public void deleteImage(String imageUrl) {
        if (cloudinary == null) {
            log.warn("Cloudinary not configured. Cannot delete image: {}", imageUrl);
            return;
        }

        try {
            // Extract public_id from URL
            String publicId = extractPublicIdFromUrl(imageUrl);
            if (publicId != null) {
                cloudinary.uploader().destroy(publicId, ObjectUtils.emptyMap());
                log.info("Image deleted successfully: {}", publicId);
            }
        } catch (Exception e) {
            log.error("Error deleting image: {}", imageUrl, e);
        }
    }

    private String extractPublicIdFromUrl(String imageUrl) {
        try {
            // Extract public_id from Cloudinary URL
            // Example: https://res.cloudinary.com/demo/image/upload/v1234567890/grocease/products/sample.jpg
            String[] parts = imageUrl.split("/");
            if (parts.length >= 2) {
                String filename = parts[parts.length - 1];
                String folder = parts[parts.length - 2];
                String parentFolder = parts[parts.length - 3];
                
                // Remove file extension
                String filenameWithoutExt = filename.substring(0, filename.lastIndexOf('.'));
                return parentFolder + "/" + folder + "/" + filenameWithoutExt;
            }
        } catch (Exception e) {
            log.error("Error extracting public_id from URL: {}", imageUrl, e);
        }
        return null;
    }
}
