@echo off
echo Running GrocEase Backend with Java directly...
echo =============================================

REM Set environment variables
set JWT_SECRET=GrocEase2024SecureJwtSecretKeyForDevelopmentAndProductionUse123456789
set EMAIL_VERIFICATION_ENABLED=false
set DB_USERNAME=postgres
set DB_PASSWORD=admin

echo Environment variables set:
echo - JWT_SECRET: [HIDDEN]
echo - EMAIL_VERIFICATION_ENABLED: %EMAIL_VERIFICATION_ENABLED%
echo - DB_USERNAME: %DB_USERNAME%
echo.

REM Check if compiled classes exist
if not exist "target\classes" (
    echo ❌ No compiled classes found in target\classes
    echo Please compile the project first with <PERSON><PERSON> or an IDE
    pause
    exit /b 1
)

echo Looking for Spring Boot dependencies...

REM Try to find Spring Boot JAR files
set SPRING_BOOT_LIBS=
for /r "target" %%f in (*.jar) do (
    set SPRING_BOOT_LIBS=!SPRING_BOOT_LIBS!;%%f
)

if "%SPRING_BOOT_LIBS%"=="" (
    echo ❌ No JAR dependencies found
    echo This project needs to be built with Maven first
    echo.
    echo Please either:
    echo 1. Install Maven and run: mvn spring-boot:run
    echo 2. Use an IDE like IntelliJ IDEA or Eclipse
    echo 3. Run: .\install-maven.bat to install Maven
    pause
    exit /b 1
)

echo ✅ Found compiled classes
echo Attempting to run with Java...

REM Try to run the application
java -cp "target\classes;%SPRING_BOOT_LIBS%" com.grocease.GrocEaseApplication

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ❌ Failed to run with Java directly
    echo This Spring Boot application needs Maven or an IDE to run properly
    echo.
    echo Recommended solutions:
    echo 1. Run: .\install-maven.bat
    echo 2. Open project in IntelliJ IDEA or Eclipse
    echo 3. Use VS Code with Java extensions
)

pause
