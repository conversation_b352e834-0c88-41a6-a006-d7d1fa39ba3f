package com.grocease.repository;

import com.grocease.dto.analytics.*;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface AnalyticsRepository extends JpaRepository<com.grocease.entity.Order, Long> {

    // Monthly Sales Data - Simplified for now
    @Query("""
        SELECT o FROM Order o
        WHERE o.createdAt >= :startDate AND o.createdAt <= :endDate
        AND o.status != 'CANCELLED'
        ORDER BY o.createdAt
    """)
    List<com.grocease.entity.Order> getMonthlySalesDataRaw(@Param("startDate") LocalDateTime startDate,
                                          @Param("endDate") LocalDateTime endDate);

    // Weekly Sales Data - Simplified for now
    @Query("""
        SELECT o FROM Order o
        WHERE o.createdAt >= :startDate AND o.createdAt <= :endDate
        AND o.status != 'CANCELLED'
        ORDER BY o.createdAt
    """)
    List<com.grocease.entity.Order> getWeeklySalesDataRaw(@Param("startDate") LocalDateTime startDate,
                                         @Param("endDate") LocalDateTime endDate);

    // Most Sold Products - Simplified
    @Query("""
        SELECT p FROM Product p
        ORDER BY p.id
    """)
    List<com.grocease.entity.Product> getMostSoldProductsRaw();

    // Popular Categories - Simplified
    @Query("""
        SELECT c FROM Category c
        ORDER BY c.id
    """)
    List<com.grocease.entity.Category> getPopularCategoriesRaw();

    // User Engagement Metrics - Simplified
    @Query("""
        SELECT u FROM User u
        ORDER BY u.id
    """)
    List<com.grocease.entity.User> getUserEngagementDataRaw();

    // Today's Revenue - Simplified
    @Query("""
        SELECT COALESCE(SUM(o.total), 0)
        FROM Order o
        WHERE o.status != 'CANCELLED'
    """)
    BigDecimal getTodayRevenue();

    // Monthly Revenue - Simplified
    @Query("""
        SELECT COALESCE(SUM(o.total), 0)
        FROM Order o
        WHERE o.status != 'CANCELLED'
    """)
    BigDecimal getMonthlyRevenue();

    // Today's Orders Count - Simplified
    @Query("""
        SELECT COUNT(o.id)
        FROM Order o
        WHERE o.status != 'CANCELLED'
    """)
    Long getTodayOrdersCount();

    // Monthly Orders Count - Simplified
    @Query("""
        SELECT COUNT(o.id)
        FROM Order o
        WHERE o.status != 'CANCELLED'
    """)
    Long getMonthlyOrdersCount();

    // Total Users Count
    @Query("SELECT COUNT(u.id) FROM User u WHERE u.isActive = true")
    Long getTotalUsersCount();

    // Active Users - Simplified
    @Query("""
        SELECT COUNT(DISTINCT u.id)
        FROM User u
        WHERE u.isActive = true
    """)
    Long getActiveUsersCount();

    // Average Order Value - Simplified
    @Query("""
        SELECT COALESCE(AVG(o.total), 0)
        FROM Order o
        WHERE o.status != 'CANCELLED'
    """)
    BigDecimal getAverageOrderValue();

    // Order Status Distribution - Simplified
    @Query("""
        SELECT o.status, COUNT(o.id)
        FROM Order o
        GROUP BY o.status
    """)
    List<Object[]> getOrderStatusDistribution();
}
