package com.grocease.controller;

import com.grocease.dto.ApiResponse;
import com.grocease.service.CloudinaryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/upload")
@RequiredArgsConstructor
@Slf4j
public class UploadController {

    private final CloudinaryService cloudinaryService;

    @GetMapping("/test")
    public ResponseEntity<ApiResponse<Map<String, Object>>> testCloudinaryConfig() {
        Map<String, Object> response = new HashMap<>();

        try {
            // Test if Cloudinary is properly configured
            boolean isConfigured = cloudinaryService.isCloudinaryConfigured();
            response.put("cloudinaryConfigured", isConfigured);
            response.put("message", isConfigured ?
                "Cloudinary is properly configured and ready for uploads" :
                "Cloudinary is not configured - uploads will use placeholder URLs");

            return ResponseEntity.ok(ApiResponse.success(response, "Cloudinary configuration test completed"));
        } catch (Exception e) {
            log.error("Error testing Cloudinary configuration", e);
            response.put("cloudinaryConfigured", false);
            response.put("error", e.getMessage());
            return ResponseEntity.ok(ApiResponse.success(response, "Cloudinary configuration test failed"));
        }
    }

    @PostMapping("/avatar")
    public ResponseEntity<ApiResponse<Map<String, String>>> uploadAvatar(@RequestParam("file") MultipartFile file) {
        try {
            validateImageFile(file);
            String imageUrl = cloudinaryService.uploadUserAvatar(file);
            
            Map<String, String> response = new HashMap<>();
            response.put("imageUrl", imageUrl);
            
            return ResponseEntity.ok(ApiResponse.success(response, "Avatar uploaded successfully"));
        } catch (IOException e) {
            log.error("Error uploading avatar", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Upload failed", "Failed to upload avatar"));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Invalid file", e.getMessage()));
        }
    }

    @PostMapping("/product")
    public ResponseEntity<ApiResponse<Map<String, String>>> uploadProductImage(@RequestParam("file") MultipartFile file) {
        try {
            validateImageFile(file);
            String imageUrl = cloudinaryService.uploadProductImage(file);
            
            Map<String, String> response = new HashMap<>();
            response.put("imageUrl", imageUrl);
            
            return ResponseEntity.ok(ApiResponse.success(response, "Product image uploaded successfully"));
        } catch (IOException e) {
            log.error("Error uploading product image", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Upload failed", "Failed to upload product image"));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Invalid file", e.getMessage()));
        }
    }

    @PostMapping("/category")
    public ResponseEntity<ApiResponse<Map<String, String>>> uploadCategoryImage(@RequestParam("file") MultipartFile file) {
        try {
            validateImageFile(file);
            String imageUrl = cloudinaryService.uploadCategoryImage(file);
            
            Map<String, String> response = new HashMap<>();
            response.put("imageUrl", imageUrl);
            
            return ResponseEntity.ok(ApiResponse.success(response, "Category image uploaded successfully"));
        } catch (IOException e) {
            log.error("Error uploading category image", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Upload failed", "Failed to upload category image"));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Invalid file", e.getMessage()));
        }
    }

    @PostMapping("/banner")
    public ResponseEntity<ApiResponse<Map<String, String>>> uploadBannerImage(@RequestParam("file") MultipartFile file) {
        try {
            validateImageFile(file);
            String imageUrl = cloudinaryService.uploadBannerImage(file);
            
            Map<String, String> response = new HashMap<>();
            response.put("imageUrl", imageUrl);
            
            return ResponseEntity.ok(ApiResponse.success(response, "Banner image uploaded successfully"));
        } catch (IOException e) {
            log.error("Error uploading banner image", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Upload failed", "Failed to upload banner image"));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Invalid file", e.getMessage()));
        }
    }

    private void validateImageFile(MultipartFile file) {
        if (file.isEmpty()) {
            throw new IllegalArgumentException("File is empty");
        }

        // Check file size (max 10MB)
        if (file.getSize() > 10 * 1024 * 1024) {
            throw new IllegalArgumentException("File size exceeds 10MB limit");
        }

        // Check file type
        String contentType = file.getContentType();
        if (contentType == null || !contentType.startsWith("image/")) {
            throw new IllegalArgumentException("File must be an image");
        }

        // Check allowed image types
        if (!contentType.equals("image/jpeg") && 
            !contentType.equals("image/png") && 
            !contentType.equals("image/gif") && 
            !contentType.equals("image/webp")) {
            throw new IllegalArgumentException("Only JPEG, PNG, GIF, and WebP images are allowed");
        }
    }
}
