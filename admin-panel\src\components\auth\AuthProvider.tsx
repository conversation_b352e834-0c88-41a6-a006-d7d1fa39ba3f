'use client'

import { useEffect } from 'react'
import { useAuthStore } from '@/store/auth'

interface AuthProviderProps {
  children: React.ReactNode
}

export default function AuthProvider({ children }: AuthProviderProps) {
  const { hasHydrated, initializeAuth, setHasHydrated } = useAuthStore()

  useEffect(() => {
    // Ensure auth state is initialized on client side
    if (typeof window !== 'undefined' && !hasHydrated) {
      initializeAuth()
      setHasHydrated(true)
    }
  }, [hasHydrated, initializeAuth, setHasHydrated])

  return <>{children}</>
}
