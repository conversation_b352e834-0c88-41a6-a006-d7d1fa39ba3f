"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/settings/page",{

/***/ "(app-pages-browser)/./src/app/settings/page.tsx":
/*!***********************************!*\
  !*** ./src/app/settings/page.tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SettingsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _store_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/auth */ \"(app-pages-browser)/./src/store/auth.ts\");\n/* harmony import */ var _components_layout_AdminLayout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/layout/AdminLayout */ \"(app-pages-browser)/./src/components/layout/AdminLayout.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_switch__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/switch */ \"(app-pages-browser)/./src/components/ui/switch.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./src/components/ui/tabs.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bell_Database_Save_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Database,Save,Shield,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Database_Save_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Database,Save,Shield,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Database_Save_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Database,Save,Shield,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Database_Save_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Database,Save,Shield,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Database_Save_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Database,Save,Shield,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction SettingsPage() {\n    _s();\n    const { user } = (0,_store_auth__WEBPACK_IMPORTED_MODULE_2__.useAuthStore)();\n    const [profileData, setProfileData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: (user === null || user === void 0 ? void 0 : user.name) || \"\",\n        email: (user === null || user === void 0 ? void 0 : user.email) || \"\",\n        phone: (user === null || user === void 0 ? void 0 : user.phone) || \"\"\n    });\n    const [notificationSettings, setNotificationSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        emailNotifications: true,\n        pushNotifications: true,\n        orderUpdates: true,\n        marketingEmails: false,\n        weeklyReports: true\n    });\n    const [systemSettings, setSystemSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        maintenanceMode: false,\n        allowRegistrations: true,\n        requireEmailVerification: true,\n        enableAnalytics: true,\n        autoBackup: true\n    });\n    const handleProfileSave = ()=>{\n        // Implementation for saving profile\n        console.log(\"Saving profile:\", profileData);\n    };\n    const handleNotificationSave = ()=>{\n        // Implementation for saving notification settings\n        console.log(\"Saving notification settings:\", notificationSettings);\n    };\n    const handleSystemSave = ()=>{\n        // Implementation for saving system settings\n        console.log(\"Saving system settings:\", systemSettings);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_AdminLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold tracking-tight\",\n                            children: \"Settings\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground\",\n                            children: \"Manage your account and system preferences\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.Tabs, {\n                    defaultValue: \"profile\",\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsList, {\n                            className: \"grid w-full grid-cols-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsTrigger, {\n                                    value: \"profile\",\n                                    children: \"Profile\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsTrigger, {\n                                    value: \"notifications\",\n                                    children: \"Notifications\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsTrigger, {\n                                    value: \"security\",\n                                    children: \"Security\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsTrigger, {\n                                    value: \"system\",\n                                    children: \"System\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsContent, {\n                            value: \"profile\",\n                            className: \"space-y-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Database_Save_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"h-5 w-5 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 90,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Profile Information\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 89,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                                children: \"Update your personal information and profile picture\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 93,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Avatar, {\n                                                        className: \"h-20 w-20\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AvatarImage, {\n                                                                src: user === null || user === void 0 ? void 0 : user.avatar,\n                                                                alt: user === null || user === void 0 ? void 0 : user.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 100,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AvatarFallback, {\n                                                                className: \"text-lg\",\n                                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_11__.getInitials)((user === null || user === void 0 ? void 0 : user.name) || \"Admin\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 101,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 99,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Upload, {\n                                                                        className: \"h-4 w-4 mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 107,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    \"Change Avatar\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 106,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-muted-foreground mt-2\",\n                                                                children: \"JPG, PNG or GIF. Max size 2MB.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 110,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 105,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 98,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_8__.Separator, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 116,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid gap-4 md:grid-cols-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                htmlFor: \"name\",\n                                                                children: \"Full Name\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 120,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                id: \"name\",\n                                                                value: profileData.name,\n                                                                onChange: (e)=>setProfileData({\n                                                                        ...profileData,\n                                                                        name: e.target.value\n                                                                    })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 121,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 119,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                htmlFor: \"email\",\n                                                                children: \"Email\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 128,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                id: \"email\",\n                                                                type: \"email\",\n                                                                value: profileData.email,\n                                                                onChange: (e)=>setProfileData({\n                                                                        ...profileData,\n                                                                        email: e.target.value\n                                                                    })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 129,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 127,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                htmlFor: \"phone\",\n                                                                children: \"Phone\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 137,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                id: \"phone\",\n                                                                value: profileData.phone,\n                                                                onChange: (e)=>setProfileData({\n                                                                        ...profileData,\n                                                                        phone: e.target.value\n                                                                    })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 138,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 136,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                onClick: handleProfileSave,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Database_Save_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 147,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Save Changes\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 146,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 87,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsContent, {\n                            value: \"notifications\",\n                            className: \"space-y-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Database_Save_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"h-5 w-5 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 159,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Notification Preferences\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                                children: \"Configure how you want to receive notifications\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-0.5\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                        children: \"Email Notifications\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 170,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: \"Receive notifications via email\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 171,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 169,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_9__.Switch, {\n                                                                checked: notificationSettings.emailNotifications,\n                                                                onCheckedChange: (checked)=>setNotificationSettings({\n                                                                        ...notificationSettings,\n                                                                        emailNotifications: checked\n                                                                    })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 175,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 168,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-0.5\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                        children: \"Push Notifications\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 185,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: \"Receive push notifications in browser\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 186,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 184,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_9__.Switch, {\n                                                                checked: notificationSettings.pushNotifications,\n                                                                onCheckedChange: (checked)=>setNotificationSettings({\n                                                                        ...notificationSettings,\n                                                                        pushNotifications: checked\n                                                                    })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 190,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 183,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-0.5\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                        children: \"Order Updates\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 200,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: \"Get notified about order status changes\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 201,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 199,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_9__.Switch, {\n                                                                checked: notificationSettings.orderUpdates,\n                                                                onCheckedChange: (checked)=>setNotificationSettings({\n                                                                        ...notificationSettings,\n                                                                        orderUpdates: checked\n                                                                    })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 205,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 198,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-0.5\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                        children: \"Marketing Emails\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 215,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: \"Receive promotional and marketing emails\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 216,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 214,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_9__.Switch, {\n                                                                checked: notificationSettings.marketingEmails,\n                                                                onCheckedChange: (checked)=>setNotificationSettings({\n                                                                        ...notificationSettings,\n                                                                        marketingEmails: checked\n                                                                    })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 220,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 213,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-0.5\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                        children: \"Weekly Reports\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 230,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: \"Receive weekly analytics reports\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 231,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 229,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_9__.Switch, {\n                                                                checked: notificationSettings.weeklyReports,\n                                                                onCheckedChange: (checked)=>setNotificationSettings({\n                                                                        ...notificationSettings,\n                                                                        weeklyReports: checked\n                                                                    })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 235,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 228,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 167,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                onClick: handleNotificationSave,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Database_Save_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 245,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Save Preferences\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 244,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsContent, {\n                            value: \"security\",\n                            className: \"space-y-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Database_Save_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"h-5 w-5 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 257,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Security Settings\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                                children: \"Manage your account security and authentication\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 260,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 255,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium mb-4\",\n                                                        children: \"Change Password\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                        htmlFor: \"current-password\",\n                                                                        children: \"Current Password\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 269,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                        id: \"current-password\",\n                                                                        type: \"password\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 270,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 268,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                        htmlFor: \"new-password\",\n                                                                        children: \"New Password\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 273,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                        id: \"new-password\",\n                                                                        type: \"password\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 274,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 272,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                        htmlFor: \"confirm-password\",\n                                                                        children: \"Confirm New Password\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 277,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                        id: \"confirm-password\",\n                                                                        type: \"password\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 278,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 276,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                className: \"w-fit\",\n                                                                children: \"Update Password\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 280,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 267,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_8__.Separator, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium mb-4\",\n                                                        children: \"Two-Factor Authentication\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 287,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm\",\n                                                                        children: \"Enable two-factor authentication for enhanced security\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 290,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: \"Currently disabled\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 291,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 289,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                variant: \"outline\",\n                                                                children: \"Enable 2FA\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 295,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 288,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 286,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_8__.Separator, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 299,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium mb-4\",\n                                                        children: \"Active Sessions\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 302,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between p-3 border rounded-lg\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium\",\n                                                                            children: \"Current Session\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 306,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-muted-foreground\",\n                                                                            children: \"Chrome on Windows • Active now\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 307,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 305,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_12__.Badge, {\n                                                                    variant: \"default\",\n                                                                    children: \"Current\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 309,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 304,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 303,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 301,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 264,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 252,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsContent, {\n                            value: \"system\",\n                            className: \"space-y-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Database_Save_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"h-5 w-5 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 322,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"System Configuration\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 321,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                                children: \"Configure system-wide settings and preferences\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 325,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-0.5\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                        children: \"Maintenance Mode\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 333,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: \"Put the system in maintenance mode\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 334,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 332,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_9__.Switch, {\n                                                                checked: systemSettings.maintenanceMode,\n                                                                onCheckedChange: (checked)=>setSystemSettings({\n                                                                        ...systemSettings,\n                                                                        maintenanceMode: checked\n                                                                    })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 338,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 331,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-0.5\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                        children: \"Allow User Registrations\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 348,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: \"Allow new users to register accounts\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 349,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 347,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_9__.Switch, {\n                                                                checked: systemSettings.allowRegistrations,\n                                                                onCheckedChange: (checked)=>setSystemSettings({\n                                                                        ...systemSettings,\n                                                                        allowRegistrations: checked\n                                                                    })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 353,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 346,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-0.5\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                        children: \"Require Email Verification\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 363,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: \"Require users to verify their email addresses\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 364,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 362,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_9__.Switch, {\n                                                                checked: systemSettings.requireEmailVerification,\n                                                                onCheckedChange: (checked)=>setSystemSettings({\n                                                                        ...systemSettings,\n                                                                        requireEmailVerification: checked\n                                                                    })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 368,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 361,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-0.5\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                        children: \"Enable Analytics\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 378,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: \"Collect analytics data for insights\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 379,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 377,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_9__.Switch, {\n                                                                checked: systemSettings.enableAnalytics,\n                                                                onCheckedChange: (checked)=>setSystemSettings({\n                                                                        ...systemSettings,\n                                                                        enableAnalytics: checked\n                                                                    })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 383,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 376,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-0.5\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                        children: \"Automatic Backups\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 393,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: \"Enable automatic daily database backups\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 394,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 392,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_9__.Switch, {\n                                                                checked: systemSettings.autoBackup,\n                                                                onCheckedChange: (checked)=>setSystemSettings({\n                                                                        ...systemSettings,\n                                                                        autoBackup: checked\n                                                                    })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 398,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 391,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                onClick: handleSystemSave,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Database_Save_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 408,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Save System Settings\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 407,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 329,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 319,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 317,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n            lineNumber: 68,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n        lineNumber: 67,\n        columnNumber: 5\n    }, this);\n}\n_s(SettingsPage, \"EMuZ0iTE6wdwY002d/oISi2pjdc=\", false, function() {\n    return [\n        _store_auth__WEBPACK_IMPORTED_MODULE_2__.useAuthStore\n    ];\n});\n_c = SettingsPage;\nvar _c;\n$RefreshReg$(_c, \"SettingsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvc2V0dGluZ3MvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUVnQztBQUNXO0FBQ2M7QUFDdUM7QUFDakQ7QUFDRjtBQUNBO0FBQ1E7QUFDTjtBQUNnQztBQVcxRDtBQUNvQjtBQUNJO0FBRTlCLFNBQVN3Qjs7SUFDdEIsTUFBTSxFQUFFQyxJQUFJLEVBQUUsR0FBR3hCLHlEQUFZQTtJQUM3QixNQUFNLENBQUN5QixhQUFhQyxlQUFlLEdBQUczQiwrQ0FBUUEsQ0FBQztRQUM3QzRCLE1BQU1ILENBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTUcsSUFBSSxLQUFJO1FBQ3BCQyxPQUFPSixDQUFBQSxpQkFBQUEsMkJBQUFBLEtBQU1JLEtBQUssS0FBSTtRQUN0QkMsT0FBT0wsQ0FBQUEsaUJBQUFBLDJCQUFBQSxLQUFNSyxLQUFLLEtBQUk7SUFDeEI7SUFFQSxNQUFNLENBQUNDLHNCQUFzQkMsd0JBQXdCLEdBQUdoQywrQ0FBUUEsQ0FBQztRQUMvRGlDLG9CQUFvQjtRQUNwQkMsbUJBQW1CO1FBQ25CQyxjQUFjO1FBQ2RDLGlCQUFpQjtRQUNqQkMsZUFBZTtJQUNqQjtJQUVBLE1BQU0sQ0FBQ0MsZ0JBQWdCQyxrQkFBa0IsR0FBR3ZDLCtDQUFRQSxDQUFDO1FBQ25Ed0MsaUJBQWlCO1FBQ2pCQyxvQkFBb0I7UUFDcEJDLDBCQUEwQjtRQUMxQkMsaUJBQWlCO1FBQ2pCQyxZQUFZO0lBQ2Q7SUFFQSxNQUFNQyxvQkFBb0I7UUFDeEIsb0NBQW9DO1FBQ3BDQyxRQUFRQyxHQUFHLENBQUMsbUJBQW1CckI7SUFDakM7SUFFQSxNQUFNc0IseUJBQXlCO1FBQzdCLGtEQUFrRDtRQUNsREYsUUFBUUMsR0FBRyxDQUFDLGlDQUFpQ2hCO0lBQy9DO0lBRUEsTUFBTWtCLG1CQUFtQjtRQUN2Qiw0Q0FBNEM7UUFDNUNILFFBQVFDLEdBQUcsQ0FBQywyQkFBMkJUO0lBQ3pDO0lBRUEscUJBQ0UsOERBQUNwQyxzRUFBV0E7a0JBQ1YsNEVBQUNnRDtZQUFJQyxXQUFVOzs4QkFFYiw4REFBQ0Q7O3NDQUNDLDhEQUFDRTs0QkFBR0QsV0FBVTtzQ0FBb0M7Ozs7OztzQ0FDbEQsOERBQUNFOzRCQUFFRixXQUFVO3NDQUF3Qjs7Ozs7Ozs7Ozs7OzhCQUt2Qyw4REFBQ3RDLHNEQUFJQTtvQkFBQ3lDLGNBQWE7b0JBQVVILFdBQVU7O3NDQUNyQyw4REFBQ3BDLDBEQUFRQTs0QkFBQ29DLFdBQVU7OzhDQUNsQiw4REFBQ25DLDZEQUFXQTtvQ0FBQ3VDLE9BQU07OENBQVU7Ozs7Ozs4Q0FDN0IsOERBQUN2Qyw2REFBV0E7b0NBQUN1QyxPQUFNOzhDQUFnQjs7Ozs7OzhDQUNuQyw4REFBQ3ZDLDZEQUFXQTtvQ0FBQ3VDLE9BQU07OENBQVc7Ozs7Ozs4Q0FDOUIsOERBQUN2Qyw2REFBV0E7b0NBQUN1QyxPQUFNOzhDQUFTOzs7Ozs7Ozs7Ozs7c0NBRzlCLDhEQUFDekMsNkRBQVdBOzRCQUFDeUMsT0FBTTs0QkFBVUosV0FBVTtzQ0FFckMsNEVBQUNoRCxxREFBSUE7O2tEQUNILDhEQUFDRywyREFBVUE7OzBEQUNULDhEQUFDQywwREFBU0E7Z0RBQUM0QyxXQUFVOztrRUFDbkIsOERBQUNsQywyR0FBSUE7d0RBQUNrQyxXQUFVOzs7Ozs7b0RBQWlCOzs7Ozs7OzBEQUduQyw4REFBQzlDLGdFQUFlQTswREFBQzs7Ozs7Ozs7Ozs7O2tEQUluQiw4REFBQ0QsNERBQVdBO3dDQUFDK0MsV0FBVTs7MERBQ3JCLDhEQUFDRDtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUNLO3dEQUFPTCxXQUFVOzswRUFDaEIsOERBQUNNO2dFQUFZQyxHQUFHLEVBQUVqQyxpQkFBQUEsMkJBQUFBLEtBQU1rQyxNQUFNO2dFQUFFQyxHQUFHLEVBQUVuQyxpQkFBQUEsMkJBQUFBLEtBQU1HLElBQUk7Ozs7OzswRUFDL0MsOERBQUNpQztnRUFBZVYsV0FBVTswRUFDdkI3Qix3REFBV0EsQ0FBQ0csQ0FBQUEsaUJBQUFBLDJCQUFBQSxLQUFNRyxJQUFJLEtBQUk7Ozs7Ozs7Ozs7OztrRUFHL0IsOERBQUNzQjs7MEVBQ0MsOERBQUMxQyx5REFBTUE7Z0VBQUNzRCxTQUFRO2dFQUFVQyxNQUFLOztrRkFDN0IsOERBQUNDO3dFQUFPYixXQUFVOzs7Ozs7b0VBQWlCOzs7Ozs7OzBFQUdyQyw4REFBQ0U7Z0VBQUVGLFdBQVU7MEVBQXFDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7MERBTXRELDhEQUFDeEMsK0RBQVNBOzs7OzswREFFViw4REFBQ3VDO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ0Q7OzBFQUNDLDhEQUFDeEMsdURBQUtBO2dFQUFDdUQsU0FBUTswRUFBTzs7Ozs7OzBFQUN0Qiw4REFBQ3hELHVEQUFLQTtnRUFDSnlELElBQUc7Z0VBQ0hYLE9BQU83QixZQUFZRSxJQUFJO2dFQUN2QnVDLFVBQVUsQ0FBQ0MsSUFBTXpDLGVBQWU7d0VBQUUsR0FBR0QsV0FBVzt3RUFBRUUsTUFBTXdDLEVBQUVDLE1BQU0sQ0FBQ2QsS0FBSztvRUFBQzs7Ozs7Ozs7Ozs7O2tFQUczRSw4REFBQ0w7OzBFQUNDLDhEQUFDeEMsdURBQUtBO2dFQUFDdUQsU0FBUTswRUFBUTs7Ozs7OzBFQUN2Qiw4REFBQ3hELHVEQUFLQTtnRUFDSnlELElBQUc7Z0VBQ0hJLE1BQUs7Z0VBQ0xmLE9BQU83QixZQUFZRyxLQUFLO2dFQUN4QnNDLFVBQVUsQ0FBQ0MsSUFBTXpDLGVBQWU7d0VBQUUsR0FBR0QsV0FBVzt3RUFBRUcsT0FBT3VDLEVBQUVDLE1BQU0sQ0FBQ2QsS0FBSztvRUFBQzs7Ozs7Ozs7Ozs7O2tFQUc1RSw4REFBQ0w7OzBFQUNDLDhEQUFDeEMsdURBQUtBO2dFQUFDdUQsU0FBUTswRUFBUTs7Ozs7OzBFQUN2Qiw4REFBQ3hELHVEQUFLQTtnRUFDSnlELElBQUc7Z0VBQ0hYLE9BQU83QixZQUFZSSxLQUFLO2dFQUN4QnFDLFVBQVUsQ0FBQ0MsSUFBTXpDLGVBQWU7d0VBQUUsR0FBR0QsV0FBVzt3RUFBRUksT0FBT3NDLEVBQUVDLE1BQU0sQ0FBQ2QsS0FBSztvRUFBQzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBEQUs5RSw4REFBQy9DLHlEQUFNQTtnREFBQytELFNBQVMxQjs7a0VBQ2YsOERBQUN4QiwyR0FBSUE7d0RBQUM4QixXQUFVOzs7Ozs7b0RBQWlCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBT3pDLDhEQUFDckMsNkRBQVdBOzRCQUFDeUMsT0FBTTs0QkFBZ0JKLFdBQVU7c0NBRTNDLDRFQUFDaEQscURBQUlBOztrREFDSCw4REFBQ0csMkRBQVVBOzswREFDVCw4REFBQ0MsMERBQVNBO2dEQUFDNEMsV0FBVTs7a0VBQ25CLDhEQUFDakMsMkdBQUlBO3dEQUFDaUMsV0FBVTs7Ozs7O29EQUFpQjs7Ozs7OzswREFHbkMsOERBQUM5QyxnRUFBZUE7MERBQUM7Ozs7Ozs7Ozs7OztrREFJbkIsOERBQUNELDREQUFXQTt3Q0FBQytDLFdBQVU7OzBEQUNyQiw4REFBQ0Q7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDRDt3REFBSUMsV0FBVTs7MEVBQ2IsOERBQUNEO2dFQUFJQyxXQUFVOztrRkFDYiw4REFBQ3pDLHVEQUFLQTtrRkFBQzs7Ozs7O2tGQUNQLDhEQUFDMkM7d0VBQUVGLFdBQVU7a0ZBQWdDOzs7Ozs7Ozs7Ozs7MEVBSS9DLDhEQUFDdkMseURBQU1BO2dFQUNMNEQsU0FBU3pDLHFCQUFxQkUsa0JBQWtCO2dFQUNoRHdDLGlCQUFpQixDQUFDRCxVQUNoQnhDLHdCQUF3Qjt3RUFBRSxHQUFHRCxvQkFBb0I7d0VBQUVFLG9CQUFvQnVDO29FQUFROzs7Ozs7Ozs7Ozs7a0VBS3JGLDhEQUFDdEI7d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDRDtnRUFBSUMsV0FBVTs7a0ZBQ2IsOERBQUN6Qyx1REFBS0E7a0ZBQUM7Ozs7OztrRkFDUCw4REFBQzJDO3dFQUFFRixXQUFVO2tGQUFnQzs7Ozs7Ozs7Ozs7OzBFQUkvQyw4REFBQ3ZDLHlEQUFNQTtnRUFDTDRELFNBQVN6QyxxQkFBcUJHLGlCQUFpQjtnRUFDL0N1QyxpQkFBaUIsQ0FBQ0QsVUFDaEJ4Qyx3QkFBd0I7d0VBQUUsR0FBR0Qsb0JBQW9CO3dFQUFFRyxtQkFBbUJzQztvRUFBUTs7Ozs7Ozs7Ozs7O2tFQUtwRiw4REFBQ3RCO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ0Q7Z0VBQUlDLFdBQVU7O2tGQUNiLDhEQUFDekMsdURBQUtBO2tGQUFDOzs7Ozs7a0ZBQ1AsOERBQUMyQzt3RUFBRUYsV0FBVTtrRkFBZ0M7Ozs7Ozs7Ozs7OzswRUFJL0MsOERBQUN2Qyx5REFBTUE7Z0VBQ0w0RCxTQUFTekMscUJBQXFCSSxZQUFZO2dFQUMxQ3NDLGlCQUFpQixDQUFDRCxVQUNoQnhDLHdCQUF3Qjt3RUFBRSxHQUFHRCxvQkFBb0I7d0VBQUVJLGNBQWNxQztvRUFBUTs7Ozs7Ozs7Ozs7O2tFQUsvRSw4REFBQ3RCO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ0Q7Z0VBQUlDLFdBQVU7O2tGQUNiLDhEQUFDekMsdURBQUtBO2tGQUFDOzs7Ozs7a0ZBQ1AsOERBQUMyQzt3RUFBRUYsV0FBVTtrRkFBZ0M7Ozs7Ozs7Ozs7OzswRUFJL0MsOERBQUN2Qyx5REFBTUE7Z0VBQ0w0RCxTQUFTekMscUJBQXFCSyxlQUFlO2dFQUM3Q3FDLGlCQUFpQixDQUFDRCxVQUNoQnhDLHdCQUF3Qjt3RUFBRSxHQUFHRCxvQkFBb0I7d0VBQUVLLGlCQUFpQm9DO29FQUFROzs7Ozs7Ozs7Ozs7a0VBS2xGLDhEQUFDdEI7d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDRDtnRUFBSUMsV0FBVTs7a0ZBQ2IsOERBQUN6Qyx1REFBS0E7a0ZBQUM7Ozs7OztrRkFDUCw4REFBQzJDO3dFQUFFRixXQUFVO2tGQUFnQzs7Ozs7Ozs7Ozs7OzBFQUkvQyw4REFBQ3ZDLHlEQUFNQTtnRUFDTDRELFNBQVN6QyxxQkFBcUJNLGFBQWE7Z0VBQzNDb0MsaUJBQWlCLENBQUNELFVBQ2hCeEMsd0JBQXdCO3dFQUFFLEdBQUdELG9CQUFvQjt3RUFBRU0sZUFBZW1DO29FQUFROzs7Ozs7Ozs7Ozs7Ozs7Ozs7MERBTWxGLDhEQUFDaEUseURBQU1BO2dEQUFDK0QsU0FBU3ZCOztrRUFDZiw4REFBQzNCLDJHQUFJQTt3REFBQzhCLFdBQVU7Ozs7OztvREFBaUI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FPekMsOERBQUNyQyw2REFBV0E7NEJBQUN5QyxPQUFNOzRCQUFXSixXQUFVO3NDQUV0Qyw0RUFBQ2hELHFEQUFJQTs7a0RBQ0gsOERBQUNHLDJEQUFVQTs7MERBQ1QsOERBQUNDLDBEQUFTQTtnREFBQzRDLFdBQVU7O2tFQUNuQiw4REFBQ2hDLDJHQUFNQTt3REFBQ2dDLFdBQVU7Ozs7OztvREFBaUI7Ozs7Ozs7MERBR3JDLDhEQUFDOUMsZ0VBQWVBOzBEQUFDOzs7Ozs7Ozs7Ozs7a0RBSW5CLDhEQUFDRCw0REFBV0E7d0NBQUMrQyxXQUFVOzswREFDckIsOERBQUNEOztrRUFDQyw4REFBQ3dCO3dEQUFHdkIsV0FBVTtrRUFBbUI7Ozs7OztrRUFDakMsOERBQUNEO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ0Q7O2tGQUNDLDhEQUFDeEMsdURBQUtBO3dFQUFDdUQsU0FBUTtrRkFBbUI7Ozs7OztrRkFDbEMsOERBQUN4RCx1REFBS0E7d0VBQUN5RCxJQUFHO3dFQUFtQkksTUFBSzs7Ozs7Ozs7Ozs7OzBFQUVwQyw4REFBQ3BCOztrRkFDQyw4REFBQ3hDLHVEQUFLQTt3RUFBQ3VELFNBQVE7a0ZBQWU7Ozs7OztrRkFDOUIsOERBQUN4RCx1REFBS0E7d0VBQUN5RCxJQUFHO3dFQUFlSSxNQUFLOzs7Ozs7Ozs7Ozs7MEVBRWhDLDhEQUFDcEI7O2tGQUNDLDhEQUFDeEMsdURBQUtBO3dFQUFDdUQsU0FBUTtrRkFBbUI7Ozs7OztrRkFDbEMsOERBQUN4RCx1REFBS0E7d0VBQUN5RCxJQUFHO3dFQUFtQkksTUFBSzs7Ozs7Ozs7Ozs7OzBFQUVwQyw4REFBQzlELHlEQUFNQTtnRUFBQzJDLFdBQVU7MEVBQVE7Ozs7Ozs7Ozs7Ozs7Ozs7OzswREFJOUIsOERBQUN4QywrREFBU0E7Ozs7OzBEQUVWLDhEQUFDdUM7O2tFQUNDLDhEQUFDd0I7d0RBQUd2QixXQUFVO2tFQUFtQjs7Ozs7O2tFQUNqQyw4REFBQ0Q7d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDRDs7a0ZBQ0MsOERBQUNHO3dFQUFFRixXQUFVO2tGQUFVOzs7Ozs7a0ZBQ3ZCLDhEQUFDRTt3RUFBRUYsV0FBVTtrRkFBZ0M7Ozs7Ozs7Ozs7OzswRUFJL0MsOERBQUMzQyx5REFBTUE7Z0VBQUNzRCxTQUFROzBFQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7MERBSTlCLDhEQUFDbkQsK0RBQVNBOzs7OzswREFFViw4REFBQ3VDOztrRUFDQyw4REFBQ3dCO3dEQUFHdkIsV0FBVTtrRUFBbUI7Ozs7OztrRUFDakMsOERBQUNEO3dEQUFJQyxXQUFVO2tFQUNiLDRFQUFDRDs0REFBSUMsV0FBVTs7OEVBQ2IsOERBQUNEOztzRkFDQyw4REFBQ0c7NEVBQUVGLFdBQVU7c0ZBQWM7Ozs7OztzRkFDM0IsOERBQUNFOzRFQUFFRixXQUFVO3NGQUFnQzs7Ozs7Ozs7Ozs7OzhFQUUvQyw4REFBQzVCLHdEQUFLQTtvRUFBQ3VDLFNBQVE7OEVBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBUXJDLDhEQUFDaEQsNkRBQVdBOzRCQUFDeUMsT0FBTTs0QkFBU0osV0FBVTtzQ0FFcEMsNEVBQUNoRCxxREFBSUE7O2tEQUNILDhEQUFDRywyREFBVUE7OzBEQUNULDhEQUFDQywwREFBU0E7Z0RBQUM0QyxXQUFVOztrRUFDbkIsOERBQUMvQiwyR0FBUUE7d0RBQUMrQixXQUFVOzs7Ozs7b0RBQWlCOzs7Ozs7OzBEQUd2Qyw4REFBQzlDLGdFQUFlQTswREFBQzs7Ozs7Ozs7Ozs7O2tEQUluQiw4REFBQ0QsNERBQVdBO3dDQUFDK0MsV0FBVTs7MERBQ3JCLDhEQUFDRDtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUNEO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ0Q7Z0VBQUlDLFdBQVU7O2tGQUNiLDhEQUFDekMsdURBQUtBO2tGQUFDOzs7Ozs7a0ZBQ1AsOERBQUMyQzt3RUFBRUYsV0FBVTtrRkFBZ0M7Ozs7Ozs7Ozs7OzswRUFJL0MsOERBQUN2Qyx5REFBTUE7Z0VBQ0w0RCxTQUFTbEMsZUFBZUUsZUFBZTtnRUFDdkNpQyxpQkFBaUIsQ0FBQ0QsVUFDaEJqQyxrQkFBa0I7d0VBQUUsR0FBR0QsY0FBYzt3RUFBRUUsaUJBQWlCZ0M7b0VBQVE7Ozs7Ozs7Ozs7OztrRUFLdEUsOERBQUN0Qjt3REFBSUMsV0FBVTs7MEVBQ2IsOERBQUNEO2dFQUFJQyxXQUFVOztrRkFDYiw4REFBQ3pDLHVEQUFLQTtrRkFBQzs7Ozs7O2tGQUNQLDhEQUFDMkM7d0VBQUVGLFdBQVU7a0ZBQWdDOzs7Ozs7Ozs7Ozs7MEVBSS9DLDhEQUFDdkMseURBQU1BO2dFQUNMNEQsU0FBU2xDLGVBQWVHLGtCQUFrQjtnRUFDMUNnQyxpQkFBaUIsQ0FBQ0QsVUFDaEJqQyxrQkFBa0I7d0VBQUUsR0FBR0QsY0FBYzt3RUFBRUcsb0JBQW9CK0I7b0VBQVE7Ozs7Ozs7Ozs7OztrRUFLekUsOERBQUN0Qjt3REFBSUMsV0FBVTs7MEVBQ2IsOERBQUNEO2dFQUFJQyxXQUFVOztrRkFDYiw4REFBQ3pDLHVEQUFLQTtrRkFBQzs7Ozs7O2tGQUNQLDhEQUFDMkM7d0VBQUVGLFdBQVU7a0ZBQWdDOzs7Ozs7Ozs7Ozs7MEVBSS9DLDhEQUFDdkMseURBQU1BO2dFQUNMNEQsU0FBU2xDLGVBQWVJLHdCQUF3QjtnRUFDaEQrQixpQkFBaUIsQ0FBQ0QsVUFDaEJqQyxrQkFBa0I7d0VBQUUsR0FBR0QsY0FBYzt3RUFBRUksMEJBQTBCOEI7b0VBQVE7Ozs7Ozs7Ozs7OztrRUFLL0UsOERBQUN0Qjt3REFBSUMsV0FBVTs7MEVBQ2IsOERBQUNEO2dFQUFJQyxXQUFVOztrRkFDYiw4REFBQ3pDLHVEQUFLQTtrRkFBQzs7Ozs7O2tGQUNQLDhEQUFDMkM7d0VBQUVGLFdBQVU7a0ZBQWdDOzs7Ozs7Ozs7Ozs7MEVBSS9DLDhEQUFDdkMseURBQU1BO2dFQUNMNEQsU0FBU2xDLGVBQWVLLGVBQWU7Z0VBQ3ZDOEIsaUJBQWlCLENBQUNELFVBQ2hCakMsa0JBQWtCO3dFQUFFLEdBQUdELGNBQWM7d0VBQUVLLGlCQUFpQjZCO29FQUFROzs7Ozs7Ozs7Ozs7a0VBS3RFLDhEQUFDdEI7d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDRDtnRUFBSUMsV0FBVTs7a0ZBQ2IsOERBQUN6Qyx1REFBS0E7a0ZBQUM7Ozs7OztrRkFDUCw4REFBQzJDO3dFQUFFRixXQUFVO2tGQUFnQzs7Ozs7Ozs7Ozs7OzBFQUkvQyw4REFBQ3ZDLHlEQUFNQTtnRUFDTDRELFNBQVNsQyxlQUFlTSxVQUFVO2dFQUNsQzZCLGlCQUFpQixDQUFDRCxVQUNoQmpDLGtCQUFrQjt3RUFBRSxHQUFHRCxjQUFjO3dFQUFFTSxZQUFZNEI7b0VBQVE7Ozs7Ozs7Ozs7Ozs7Ozs7OzswREFNbkUsOERBQUNoRSx5REFBTUE7Z0RBQUMrRCxTQUFTdEI7O2tFQUNmLDhEQUFDNUIsMkdBQUlBO3dEQUFDOEIsV0FBVTs7Ozs7O29EQUFpQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFVbkQ7R0F2WXdCM0I7O1FBQ0x2QixxREFBWUE7OztLQURQdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2FwcC9zZXR0aW5ncy9wYWdlLnRzeD80N2U5Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0J1xuaW1wb3J0IHsgdXNlQXV0aFN0b3JlIH0gZnJvbSAnQC9zdG9yZS9hdXRoJ1xuaW1wb3J0IEFkbWluTGF5b3V0IGZyb20gJ0AvY29tcG9uZW50cy9sYXlvdXQvQWRtaW5MYXlvdXQnXG5pbXBvcnQgeyBDYXJkLCBDYXJkQ29udGVudCwgQ2FyZERlc2NyaXB0aW9uLCBDYXJkSGVhZGVyLCBDYXJkVGl0bGUgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvY2FyZCdcbmltcG9ydCB7IEJ1dHRvbiB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9idXR0b24nXG5pbXBvcnQgeyBJbnB1dCB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9pbnB1dCdcbmltcG9ydCB7IExhYmVsIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2xhYmVsJ1xuaW1wb3J0IHsgU2VwYXJhdG9yIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL3NlcGFyYXRvcidcbmltcG9ydCB7IFN3aXRjaCB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9zd2l0Y2gnXG5pbXBvcnQgeyBUYWJzLCBUYWJzQ29udGVudCwgVGFic0xpc3QsIFRhYnNUcmlnZ2VyIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL3RhYnMnXG5pbXBvcnQgeyBJbWFnZVVwbG9hZCB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9pbWFnZS11cGxvYWQnXG5pbXBvcnQge1xuICBVc2VyLFxuICBCZWxsLFxuICBTaGllbGQsXG4gIFBhbGV0dGUsXG4gIERhdGFiYXNlLFxuICBNYWlsLFxuICBQaG9uZSxcbiAgU2F2ZVxufSBmcm9tICdsdWNpZGUtcmVhY3QnXG5pbXBvcnQgeyBnZXRJbml0aWFscyB9IGZyb20gJ0AvbGliL3V0aWxzJ1xuaW1wb3J0IHsgQmFkZ2UgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvYmFkZ2UnXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFNldHRpbmdzUGFnZSgpIHtcbiAgY29uc3QgeyB1c2VyIH0gPSB1c2VBdXRoU3RvcmUoKVxuICBjb25zdCBbcHJvZmlsZURhdGEsIHNldFByb2ZpbGVEYXRhXSA9IHVzZVN0YXRlKHtcbiAgICBuYW1lOiB1c2VyPy5uYW1lIHx8ICcnLFxuICAgIGVtYWlsOiB1c2VyPy5lbWFpbCB8fCAnJyxcbiAgICBwaG9uZTogdXNlcj8ucGhvbmUgfHwgJycsXG4gIH0pXG5cbiAgY29uc3QgW25vdGlmaWNhdGlvblNldHRpbmdzLCBzZXROb3RpZmljYXRpb25TZXR0aW5nc10gPSB1c2VTdGF0ZSh7XG4gICAgZW1haWxOb3RpZmljYXRpb25zOiB0cnVlLFxuICAgIHB1c2hOb3RpZmljYXRpb25zOiB0cnVlLFxuICAgIG9yZGVyVXBkYXRlczogdHJ1ZSxcbiAgICBtYXJrZXRpbmdFbWFpbHM6IGZhbHNlLFxuICAgIHdlZWtseVJlcG9ydHM6IHRydWUsXG4gIH0pXG5cbiAgY29uc3QgW3N5c3RlbVNldHRpbmdzLCBzZXRTeXN0ZW1TZXR0aW5nc10gPSB1c2VTdGF0ZSh7XG4gICAgbWFpbnRlbmFuY2VNb2RlOiBmYWxzZSxcbiAgICBhbGxvd1JlZ2lzdHJhdGlvbnM6IHRydWUsXG4gICAgcmVxdWlyZUVtYWlsVmVyaWZpY2F0aW9uOiB0cnVlLFxuICAgIGVuYWJsZUFuYWx5dGljczogdHJ1ZSxcbiAgICBhdXRvQmFja3VwOiB0cnVlLFxuICB9KVxuXG4gIGNvbnN0IGhhbmRsZVByb2ZpbGVTYXZlID0gKCkgPT4ge1xuICAgIC8vIEltcGxlbWVudGF0aW9uIGZvciBzYXZpbmcgcHJvZmlsZVxuICAgIGNvbnNvbGUubG9nKCdTYXZpbmcgcHJvZmlsZTonLCBwcm9maWxlRGF0YSlcbiAgfVxuXG4gIGNvbnN0IGhhbmRsZU5vdGlmaWNhdGlvblNhdmUgPSAoKSA9PiB7XG4gICAgLy8gSW1wbGVtZW50YXRpb24gZm9yIHNhdmluZyBub3RpZmljYXRpb24gc2V0dGluZ3NcbiAgICBjb25zb2xlLmxvZygnU2F2aW5nIG5vdGlmaWNhdGlvbiBzZXR0aW5nczonLCBub3RpZmljYXRpb25TZXR0aW5ncylcbiAgfVxuXG4gIGNvbnN0IGhhbmRsZVN5c3RlbVNhdmUgPSAoKSA9PiB7XG4gICAgLy8gSW1wbGVtZW50YXRpb24gZm9yIHNhdmluZyBzeXN0ZW0gc2V0dGluZ3NcbiAgICBjb25zb2xlLmxvZygnU2F2aW5nIHN5c3RlbSBzZXR0aW5nczonLCBzeXN0ZW1TZXR0aW5ncylcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPEFkbWluTGF5b3V0PlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgICAgey8qIEhlYWRlciAqL31cbiAgICAgICAgPGRpdj5cbiAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC0zeGwgZm9udC1ib2xkIHRyYWNraW5nLXRpZ2h0XCI+U2V0dGluZ3M8L2gxPlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPlxuICAgICAgICAgICAgTWFuYWdlIHlvdXIgYWNjb3VudCBhbmQgc3lzdGVtIHByZWZlcmVuY2VzXG4gICAgICAgICAgPC9wPlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICA8VGFicyBkZWZhdWx0VmFsdWU9XCJwcm9maWxlXCIgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICAgICAgPFRhYnNMaXN0IGNsYXNzTmFtZT1cImdyaWQgdy1mdWxsIGdyaWQtY29scy00XCI+XG4gICAgICAgICAgICA8VGFic1RyaWdnZXIgdmFsdWU9XCJwcm9maWxlXCI+UHJvZmlsZTwvVGFic1RyaWdnZXI+XG4gICAgICAgICAgICA8VGFic1RyaWdnZXIgdmFsdWU9XCJub3RpZmljYXRpb25zXCI+Tm90aWZpY2F0aW9uczwvVGFic1RyaWdnZXI+XG4gICAgICAgICAgICA8VGFic1RyaWdnZXIgdmFsdWU9XCJzZWN1cml0eVwiPlNlY3VyaXR5PC9UYWJzVHJpZ2dlcj5cbiAgICAgICAgICAgIDxUYWJzVHJpZ2dlciB2YWx1ZT1cInN5c3RlbVwiPlN5c3RlbTwvVGFic1RyaWdnZXI+XG4gICAgICAgICAgPC9UYWJzTGlzdD5cblxuICAgICAgICAgIDxUYWJzQ29udGVudCB2YWx1ZT1cInByb2ZpbGVcIiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgICAgICAgIHsvKiBQcm9maWxlIFNldHRpbmdzICovfVxuICAgICAgICAgICAgPENhcmQ+XG4gICAgICAgICAgICAgIDxDYXJkSGVhZGVyPlxuICAgICAgICAgICAgICAgIDxDYXJkVGl0bGUgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgIDxVc2VyIGNsYXNzTmFtZT1cImgtNSB3LTUgbXItMlwiIC8+XG4gICAgICAgICAgICAgICAgICBQcm9maWxlIEluZm9ybWF0aW9uXG4gICAgICAgICAgICAgICAgPC9DYXJkVGl0bGU+XG4gICAgICAgICAgICAgICAgPENhcmREZXNjcmlwdGlvbj5cbiAgICAgICAgICAgICAgICAgIFVwZGF0ZSB5b3VyIHBlcnNvbmFsIGluZm9ybWF0aW9uIGFuZCBwcm9maWxlIHBpY3R1cmVcbiAgICAgICAgICAgICAgICA8L0NhcmREZXNjcmlwdGlvbj5cbiAgICAgICAgICAgICAgPC9DYXJkSGVhZGVyPlxuICAgICAgICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTRcIj5cbiAgICAgICAgICAgICAgICAgIDxBdmF0YXIgY2xhc3NOYW1lPVwiaC0yMCB3LTIwXCI+XG4gICAgICAgICAgICAgICAgICAgIDxBdmF0YXJJbWFnZSBzcmM9e3VzZXI/LmF2YXRhcn0gYWx0PXt1c2VyPy5uYW1lfSAvPlxuICAgICAgICAgICAgICAgICAgICA8QXZhdGFyRmFsbGJhY2sgY2xhc3NOYW1lPVwidGV4dC1sZ1wiPlxuICAgICAgICAgICAgICAgICAgICAgIHtnZXRJbml0aWFscyh1c2VyPy5uYW1lIHx8ICdBZG1pbicpfVxuICAgICAgICAgICAgICAgICAgICA8L0F2YXRhckZhbGxiYWNrPlxuICAgICAgICAgICAgICAgICAgPC9BdmF0YXI+XG4gICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICA8QnV0dG9uIHZhcmlhbnQ9XCJvdXRsaW5lXCIgc2l6ZT1cInNtXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPFVwbG9hZCBjbGFzc05hbWU9XCJoLTQgdy00IG1yLTJcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgIENoYW5nZSBBdmF0YXJcbiAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1tdXRlZC1mb3JlZ3JvdW5kIG10LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICBKUEcsIFBORyBvciBHSUYuIE1heCBzaXplIDJNQi5cbiAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICA8U2VwYXJhdG9yIC8+XG5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ2FwLTQgbWQ6Z3JpZC1jb2xzLTJcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwibmFtZVwiPkZ1bGwgTmFtZTwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICAgICAgICAgIGlkPVwibmFtZVwiXG4gICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3Byb2ZpbGVEYXRhLm5hbWV9XG4gICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRQcm9maWxlRGF0YSh7IC4uLnByb2ZpbGVEYXRhLCBuYW1lOiBlLnRhcmdldC52YWx1ZSB9KX1cbiAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJlbWFpbFwiPkVtYWlsPC9MYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgICAgICAgICAgaWQ9XCJlbWFpbFwiXG4gICAgICAgICAgICAgICAgICAgICAgdHlwZT1cImVtYWlsXCJcbiAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17cHJvZmlsZURhdGEuZW1haWx9XG4gICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRQcm9maWxlRGF0YSh7IC4uLnByb2ZpbGVEYXRhLCBlbWFpbDogZS50YXJnZXQudmFsdWUgfSl9XG4gICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwicGhvbmVcIj5QaG9uZTwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICAgICAgICAgIGlkPVwicGhvbmVcIlxuICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtwcm9maWxlRGF0YS5waG9uZX1cbiAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFByb2ZpbGVEYXRhKHsgLi4ucHJvZmlsZURhdGEsIHBob25lOiBlLnRhcmdldC52YWx1ZSB9KX1cbiAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgPEJ1dHRvbiBvbkNsaWNrPXtoYW5kbGVQcm9maWxlU2F2ZX0+XG4gICAgICAgICAgICAgICAgICA8U2F2ZSBjbGFzc05hbWU9XCJoLTQgdy00IG1yLTJcIiAvPlxuICAgICAgICAgICAgICAgICAgU2F2ZSBDaGFuZ2VzXG4gICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgICA8L0NhcmQ+XG4gICAgICAgICAgPC9UYWJzQ29udGVudD5cblxuICAgICAgICAgIDxUYWJzQ29udGVudCB2YWx1ZT1cIm5vdGlmaWNhdGlvbnNcIiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgICAgICAgIHsvKiBOb3RpZmljYXRpb24gU2V0dGluZ3MgKi99XG4gICAgICAgICAgICA8Q2FyZD5cbiAgICAgICAgICAgICAgPENhcmRIZWFkZXI+XG4gICAgICAgICAgICAgICAgPENhcmRUaXRsZSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgPEJlbGwgY2xhc3NOYW1lPVwiaC01IHctNSBtci0yXCIgLz5cbiAgICAgICAgICAgICAgICAgIE5vdGlmaWNhdGlvbiBQcmVmZXJlbmNlc1xuICAgICAgICAgICAgICAgIDwvQ2FyZFRpdGxlPlxuICAgICAgICAgICAgICAgIDxDYXJkRGVzY3JpcHRpb24+XG4gICAgICAgICAgICAgICAgICBDb25maWd1cmUgaG93IHlvdSB3YW50IHRvIHJlY2VpdmUgbm90aWZpY2F0aW9uc1xuICAgICAgICAgICAgICAgIDwvQ2FyZERlc2NyaXB0aW9uPlxuICAgICAgICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgICAgICAgIDxDYXJkQ29udGVudCBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTAuNVwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxMYWJlbD5FbWFpbCBOb3RpZmljYXRpb25zPC9MYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgUmVjZWl2ZSBub3RpZmljYXRpb25zIHZpYSBlbWFpbFxuICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxTd2l0Y2hcbiAgICAgICAgICAgICAgICAgICAgICBjaGVja2VkPXtub3RpZmljYXRpb25TZXR0aW5ncy5lbWFpbE5vdGlmaWNhdGlvbnN9XG4gICAgICAgICAgICAgICAgICAgICAgb25DaGVja2VkQ2hhbmdlPXsoY2hlY2tlZCkgPT5cbiAgICAgICAgICAgICAgICAgICAgICAgIHNldE5vdGlmaWNhdGlvblNldHRpbmdzKHsgLi4ubm90aWZpY2F0aW9uU2V0dGluZ3MsIGVtYWlsTm90aWZpY2F0aW9uczogY2hlY2tlZCB9KVxuICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMC41XCI+XG4gICAgICAgICAgICAgICAgICAgICAgPExhYmVsPlB1c2ggTm90aWZpY2F0aW9uczwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIFJlY2VpdmUgcHVzaCBub3RpZmljYXRpb25zIGluIGJyb3dzZXJcbiAgICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8U3dpdGNoXG4gICAgICAgICAgICAgICAgICAgICAgY2hlY2tlZD17bm90aWZpY2F0aW9uU2V0dGluZ3MucHVzaE5vdGlmaWNhdGlvbnN9XG4gICAgICAgICAgICAgICAgICAgICAgb25DaGVja2VkQ2hhbmdlPXsoY2hlY2tlZCkgPT5cbiAgICAgICAgICAgICAgICAgICAgICAgIHNldE5vdGlmaWNhdGlvblNldHRpbmdzKHsgLi4ubm90aWZpY2F0aW9uU2V0dGluZ3MsIHB1c2hOb3RpZmljYXRpb25zOiBjaGVja2VkIH0pXG4gICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0wLjVcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8TGFiZWw+T3JkZXIgVXBkYXRlczwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIEdldCBub3RpZmllZCBhYm91dCBvcmRlciBzdGF0dXMgY2hhbmdlc1xuICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxTd2l0Y2hcbiAgICAgICAgICAgICAgICAgICAgICBjaGVja2VkPXtub3RpZmljYXRpb25TZXR0aW5ncy5vcmRlclVwZGF0ZXN9XG4gICAgICAgICAgICAgICAgICAgICAgb25DaGVja2VkQ2hhbmdlPXsoY2hlY2tlZCkgPT5cbiAgICAgICAgICAgICAgICAgICAgICAgIHNldE5vdGlmaWNhdGlvblNldHRpbmdzKHsgLi4ubm90aWZpY2F0aW9uU2V0dGluZ3MsIG9yZGVyVXBkYXRlczogY2hlY2tlZCB9KVxuICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMC41XCI+XG4gICAgICAgICAgICAgICAgICAgICAgPExhYmVsPk1hcmtldGluZyBFbWFpbHM8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICBSZWNlaXZlIHByb21vdGlvbmFsIGFuZCBtYXJrZXRpbmcgZW1haWxzXG4gICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPFN3aXRjaFxuICAgICAgICAgICAgICAgICAgICAgIGNoZWNrZWQ9e25vdGlmaWNhdGlvblNldHRpbmdzLm1hcmtldGluZ0VtYWlsc31cbiAgICAgICAgICAgICAgICAgICAgICBvbkNoZWNrZWRDaGFuZ2U9eyhjaGVja2VkKSA9PlxuICAgICAgICAgICAgICAgICAgICAgICAgc2V0Tm90aWZpY2F0aW9uU2V0dGluZ3MoeyAuLi5ub3RpZmljYXRpb25TZXR0aW5ncywgbWFya2V0aW5nRW1haWxzOiBjaGVja2VkIH0pXG4gICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0wLjVcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8TGFiZWw+V2Vla2x5IFJlcG9ydHM8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICBSZWNlaXZlIHdlZWtseSBhbmFseXRpY3MgcmVwb3J0c1xuICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxTd2l0Y2hcbiAgICAgICAgICAgICAgICAgICAgICBjaGVja2VkPXtub3RpZmljYXRpb25TZXR0aW5ncy53ZWVrbHlSZXBvcnRzfVxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2hlY2tlZENoYW5nZT17KGNoZWNrZWQpID0+XG4gICAgICAgICAgICAgICAgICAgICAgICBzZXROb3RpZmljYXRpb25TZXR0aW5ncyh7IC4uLm5vdGlmaWNhdGlvblNldHRpbmdzLCB3ZWVrbHlSZXBvcnRzOiBjaGVja2VkIH0pXG4gICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICA8QnV0dG9uIG9uQ2xpY2s9e2hhbmRsZU5vdGlmaWNhdGlvblNhdmV9PlxuICAgICAgICAgICAgICAgICAgPFNhdmUgY2xhc3NOYW1lPVwiaC00IHctNCBtci0yXCIgLz5cbiAgICAgICAgICAgICAgICAgIFNhdmUgUHJlZmVyZW5jZXNcbiAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgICAgIDwvQ2FyZD5cbiAgICAgICAgICA8L1RhYnNDb250ZW50PlxuXG4gICAgICAgICAgPFRhYnNDb250ZW50IHZhbHVlPVwic2VjdXJpdHlcIiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgICAgICAgIHsvKiBTZWN1cml0eSBTZXR0aW5ncyAqL31cbiAgICAgICAgICAgIDxDYXJkPlxuICAgICAgICAgICAgICA8Q2FyZEhlYWRlcj5cbiAgICAgICAgICAgICAgICA8Q2FyZFRpdGxlIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICA8U2hpZWxkIGNsYXNzTmFtZT1cImgtNSB3LTUgbXItMlwiIC8+XG4gICAgICAgICAgICAgICAgICBTZWN1cml0eSBTZXR0aW5nc1xuICAgICAgICAgICAgICAgIDwvQ2FyZFRpdGxlPlxuICAgICAgICAgICAgICAgIDxDYXJkRGVzY3JpcHRpb24+XG4gICAgICAgICAgICAgICAgICBNYW5hZ2UgeW91ciBhY2NvdW50IHNlY3VyaXR5IGFuZCBhdXRoZW50aWNhdGlvblxuICAgICAgICAgICAgICAgIDwvQ2FyZERlc2NyaXB0aW9uPlxuICAgICAgICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgICAgICAgIDxDYXJkQ29udGVudCBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIG1iLTRcIj5DaGFuZ2UgUGFzc3dvcmQ8L2g0PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdhcC00XCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJjdXJyZW50LXBhc3N3b3JkXCI+Q3VycmVudCBQYXNzd29yZDwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgPElucHV0IGlkPVwiY3VycmVudC1wYXNzd29yZFwiIHR5cGU9XCJwYXNzd29yZFwiIC8+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwibmV3LXBhc3N3b3JkXCI+TmV3IFBhc3N3b3JkPC9MYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICA8SW5wdXQgaWQ9XCJuZXctcGFzc3dvcmRcIiB0eXBlPVwicGFzc3dvcmRcIiAvPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cImNvbmZpcm0tcGFzc3dvcmRcIj5Db25maXJtIE5ldyBQYXNzd29yZDwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgPElucHV0IGlkPVwiY29uZmlybS1wYXNzd29yZFwiIHR5cGU9XCJwYXNzd29yZFwiIC8+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8QnV0dG9uIGNsYXNzTmFtZT1cInctZml0XCI+VXBkYXRlIFBhc3N3b3JkPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgIDxTZXBhcmF0b3IgLz5cblxuICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gbWItNFwiPlR3by1GYWN0b3IgQXV0aGVudGljYXRpb248L2g0PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtXCI+RW5hYmxlIHR3by1mYWN0b3IgYXV0aGVudGljYXRpb24gZm9yIGVuaGFuY2VkIHNlY3VyaXR5PC9wPlxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICBDdXJyZW50bHkgZGlzYWJsZWRcbiAgICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8QnV0dG9uIHZhcmlhbnQ9XCJvdXRsaW5lXCI+RW5hYmxlIDJGQTwvQnV0dG9uPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICA8U2VwYXJhdG9yIC8+XG5cbiAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIG1iLTRcIj5BY3RpdmUgU2Vzc2lvbnM8L2g0PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gcC0zIGJvcmRlciByb3VuZGVkLWxnXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtXCI+Q3VycmVudCBTZXNzaW9uPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5DaHJvbWUgb24gV2luZG93cyDigKIgQWN0aXZlIG5vdzwvcD5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8QmFkZ2UgdmFyaWFudD1cImRlZmF1bHRcIj5DdXJyZW50PC9CYWRnZT5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgICAgIDwvQ2FyZD5cbiAgICAgICAgICA8L1RhYnNDb250ZW50PlxuXG4gICAgICAgICAgPFRhYnNDb250ZW50IHZhbHVlPVwic3lzdGVtXCIgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICAgICAgICB7LyogU3lzdGVtIFNldHRpbmdzICovfVxuICAgICAgICAgICAgPENhcmQ+XG4gICAgICAgICAgICAgIDxDYXJkSGVhZGVyPlxuICAgICAgICAgICAgICAgIDxDYXJkVGl0bGUgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgIDxEYXRhYmFzZSBjbGFzc05hbWU9XCJoLTUgdy01IG1yLTJcIiAvPlxuICAgICAgICAgICAgICAgICAgU3lzdGVtIENvbmZpZ3VyYXRpb25cbiAgICAgICAgICAgICAgICA8L0NhcmRUaXRsZT5cbiAgICAgICAgICAgICAgICA8Q2FyZERlc2NyaXB0aW9uPlxuICAgICAgICAgICAgICAgICAgQ29uZmlndXJlIHN5c3RlbS13aWRlIHNldHRpbmdzIGFuZCBwcmVmZXJlbmNlc1xuICAgICAgICAgICAgICAgIDwvQ2FyZERlc2NyaXB0aW9uPlxuICAgICAgICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgICAgICAgIDxDYXJkQ29udGVudCBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTAuNVwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxMYWJlbD5NYWludGVuYW5jZSBNb2RlPC9MYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgUHV0IHRoZSBzeXN0ZW0gaW4gbWFpbnRlbmFuY2UgbW9kZVxuICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxTd2l0Y2hcbiAgICAgICAgICAgICAgICAgICAgICBjaGVja2VkPXtzeXN0ZW1TZXR0aW5ncy5tYWludGVuYW5jZU1vZGV9XG4gICAgICAgICAgICAgICAgICAgICAgb25DaGVja2VkQ2hhbmdlPXsoY2hlY2tlZCkgPT5cbiAgICAgICAgICAgICAgICAgICAgICAgIHNldFN5c3RlbVNldHRpbmdzKHsgLi4uc3lzdGVtU2V0dGluZ3MsIG1haW50ZW5hbmNlTW9kZTogY2hlY2tlZCB9KVxuICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMC41XCI+XG4gICAgICAgICAgICAgICAgICAgICAgPExhYmVsPkFsbG93IFVzZXIgUmVnaXN0cmF0aW9uczwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIEFsbG93IG5ldyB1c2VycyB0byByZWdpc3RlciBhY2NvdW50c1xuICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxTd2l0Y2hcbiAgICAgICAgICAgICAgICAgICAgICBjaGVja2VkPXtzeXN0ZW1TZXR0aW5ncy5hbGxvd1JlZ2lzdHJhdGlvbnN9XG4gICAgICAgICAgICAgICAgICAgICAgb25DaGVja2VkQ2hhbmdlPXsoY2hlY2tlZCkgPT5cbiAgICAgICAgICAgICAgICAgICAgICAgIHNldFN5c3RlbVNldHRpbmdzKHsgLi4uc3lzdGVtU2V0dGluZ3MsIGFsbG93UmVnaXN0cmF0aW9uczogY2hlY2tlZCB9KVxuICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMC41XCI+XG4gICAgICAgICAgICAgICAgICAgICAgPExhYmVsPlJlcXVpcmUgRW1haWwgVmVyaWZpY2F0aW9uPC9MYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgUmVxdWlyZSB1c2VycyB0byB2ZXJpZnkgdGhlaXIgZW1haWwgYWRkcmVzc2VzXG4gICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPFN3aXRjaFxuICAgICAgICAgICAgICAgICAgICAgIGNoZWNrZWQ9e3N5c3RlbVNldHRpbmdzLnJlcXVpcmVFbWFpbFZlcmlmaWNhdGlvbn1cbiAgICAgICAgICAgICAgICAgICAgICBvbkNoZWNrZWRDaGFuZ2U9eyhjaGVja2VkKSA9PlxuICAgICAgICAgICAgICAgICAgICAgICAgc2V0U3lzdGVtU2V0dGluZ3MoeyAuLi5zeXN0ZW1TZXR0aW5ncywgcmVxdWlyZUVtYWlsVmVyaWZpY2F0aW9uOiBjaGVja2VkIH0pXG4gICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0wLjVcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8TGFiZWw+RW5hYmxlIEFuYWx5dGljczwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIENvbGxlY3QgYW5hbHl0aWNzIGRhdGEgZm9yIGluc2lnaHRzXG4gICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPFN3aXRjaFxuICAgICAgICAgICAgICAgICAgICAgIGNoZWNrZWQ9e3N5c3RlbVNldHRpbmdzLmVuYWJsZUFuYWx5dGljc31cbiAgICAgICAgICAgICAgICAgICAgICBvbkNoZWNrZWRDaGFuZ2U9eyhjaGVja2VkKSA9PlxuICAgICAgICAgICAgICAgICAgICAgICAgc2V0U3lzdGVtU2V0dGluZ3MoeyAuLi5zeXN0ZW1TZXR0aW5ncywgZW5hYmxlQW5hbHl0aWNzOiBjaGVja2VkIH0pXG4gICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0wLjVcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8TGFiZWw+QXV0b21hdGljIEJhY2t1cHM8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICBFbmFibGUgYXV0b21hdGljIGRhaWx5IGRhdGFiYXNlIGJhY2t1cHNcbiAgICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8U3dpdGNoXG4gICAgICAgICAgICAgICAgICAgICAgY2hlY2tlZD17c3lzdGVtU2V0dGluZ3MuYXV0b0JhY2t1cH1cbiAgICAgICAgICAgICAgICAgICAgICBvbkNoZWNrZWRDaGFuZ2U9eyhjaGVja2VkKSA9PlxuICAgICAgICAgICAgICAgICAgICAgICAgc2V0U3lzdGVtU2V0dGluZ3MoeyAuLi5zeXN0ZW1TZXR0aW5ncywgYXV0b0JhY2t1cDogY2hlY2tlZCB9KVxuICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgPEJ1dHRvbiBvbkNsaWNrPXtoYW5kbGVTeXN0ZW1TYXZlfT5cbiAgICAgICAgICAgICAgICAgIDxTYXZlIGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMlwiIC8+XG4gICAgICAgICAgICAgICAgICBTYXZlIFN5c3RlbSBTZXR0aW5nc1xuICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICAgICAgPC9DYXJkPlxuICAgICAgICAgIDwvVGFic0NvbnRlbnQ+XG4gICAgICAgIDwvVGFicz5cbiAgICAgIDwvZGl2PlxuICAgIDwvQWRtaW5MYXlvdXQ+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUF1dGhTdG9yZSIsIkFkbWluTGF5b3V0IiwiQ2FyZCIsIkNhcmRDb250ZW50IiwiQ2FyZERlc2NyaXB0aW9uIiwiQ2FyZEhlYWRlciIsIkNhcmRUaXRsZSIsIkJ1dHRvbiIsIklucHV0IiwiTGFiZWwiLCJTZXBhcmF0b3IiLCJTd2l0Y2giLCJUYWJzIiwiVGFic0NvbnRlbnQiLCJUYWJzTGlzdCIsIlRhYnNUcmlnZ2VyIiwiVXNlciIsIkJlbGwiLCJTaGllbGQiLCJEYXRhYmFzZSIsIlNhdmUiLCJnZXRJbml0aWFscyIsIkJhZGdlIiwiU2V0dGluZ3NQYWdlIiwidXNlciIsInByb2ZpbGVEYXRhIiwic2V0UHJvZmlsZURhdGEiLCJuYW1lIiwiZW1haWwiLCJwaG9uZSIsIm5vdGlmaWNhdGlvblNldHRpbmdzIiwic2V0Tm90aWZpY2F0aW9uU2V0dGluZ3MiLCJlbWFpbE5vdGlmaWNhdGlvbnMiLCJwdXNoTm90aWZpY2F0aW9ucyIsIm9yZGVyVXBkYXRlcyIsIm1hcmtldGluZ0VtYWlscyIsIndlZWtseVJlcG9ydHMiLCJzeXN0ZW1TZXR0aW5ncyIsInNldFN5c3RlbVNldHRpbmdzIiwibWFpbnRlbmFuY2VNb2RlIiwiYWxsb3dSZWdpc3RyYXRpb25zIiwicmVxdWlyZUVtYWlsVmVyaWZpY2F0aW9uIiwiZW5hYmxlQW5hbHl0aWNzIiwiYXV0b0JhY2t1cCIsImhhbmRsZVByb2ZpbGVTYXZlIiwiY29uc29sZSIsImxvZyIsImhhbmRsZU5vdGlmaWNhdGlvblNhdmUiLCJoYW5kbGVTeXN0ZW1TYXZlIiwiZGl2IiwiY2xhc3NOYW1lIiwiaDEiLCJwIiwiZGVmYXVsdFZhbHVlIiwidmFsdWUiLCJBdmF0YXIiLCJBdmF0YXJJbWFnZSIsInNyYyIsImF2YXRhciIsImFsdCIsIkF2YXRhckZhbGxiYWNrIiwidmFyaWFudCIsInNpemUiLCJVcGxvYWQiLCJodG1sRm9yIiwiaWQiLCJvbkNoYW5nZSIsImUiLCJ0YXJnZXQiLCJ0eXBlIiwib25DbGljayIsImNoZWNrZWQiLCJvbkNoZWNrZWRDaGFuZ2UiLCJoNCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/settings/page.tsx\n"));

/***/ })

});