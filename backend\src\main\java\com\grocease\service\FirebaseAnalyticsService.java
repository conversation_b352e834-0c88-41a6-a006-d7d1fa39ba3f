package com.grocease.service;

import com.google.firebase.FirebaseApp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
@Slf4j
@ConditionalOnProperty(name = "firebase.enabled", havingValue = "true", matchIfMissing = false)
public class FirebaseAnalyticsService {

    private final FirebaseApp firebaseApp;

    public FirebaseAnalyticsService(@Autowired(required = false) FirebaseApp firebaseApp) {
        this.firebaseApp = firebaseApp;
        if (firebaseApp != null) {
            log.info("Firebase Analytics Service initialized successfully");
        } else {
            log.warn("Firebase Analytics Service initialized but Firebase is not available");
        }
    }

    public void trackUserRegistration(Long userId, String email) {
        if (firebaseApp == null) {
            log.debug("Firebase not available, skipping user registration tracking");
            return;
        }

        try {
            Map<String, Object> eventData = new HashMap<>();
            eventData.put("user_id", userId);
            eventData.put("email", email);
            eventData.put("timestamp", System.currentTimeMillis());
            
            log.info("User registration tracked: userId={}, email={}", userId, email);
            // Note: Firebase Admin SDK doesn't directly support Analytics events
            // This would typically be handled on the client side
        } catch (Exception e) {
            log.error("Failed to track user registration", e);
        }
    }

    public void trackOrderPlaced(Long orderId, Long userId, Double orderValue, String paymentMethod) {
        if (firebaseApp == null) {
            log.debug("Firebase not available, skipping order tracking");
            return;
        }

        try {
            Map<String, Object> eventData = new HashMap<>();
            eventData.put("order_id", orderId);
            eventData.put("user_id", userId);
            eventData.put("order_value", orderValue);
            eventData.put("payment_method", paymentMethod);
            eventData.put("timestamp", System.currentTimeMillis());
            
            log.info("Order placed tracked: orderId={}, userId={}, value={}", orderId, userId, orderValue);
        } catch (Exception e) {
            log.error("Failed to track order placement", e);
        }
    }

    public void trackProductView(Long productId, String productName, Long userId) {
        if (firebaseApp == null) {
            log.debug("Firebase not available, skipping product view tracking");
            return;
        }

        try {
            Map<String, Object> eventData = new HashMap<>();
            eventData.put("product_id", productId);
            eventData.put("product_name", productName);
            eventData.put("user_id", userId);
            eventData.put("timestamp", System.currentTimeMillis());
            
            log.info("Product view tracked: productId={}, productName={}, userId={}", productId, productName, userId);
        } catch (Exception e) {
            log.error("Failed to track product view", e);
        }
    }

    public void trackCartAddition(Long productId, String productName, Long userId, Integer quantity) {
        if (firebaseApp == null) {
            log.debug("Firebase not available, skipping cart addition tracking");
            return;
        }

        try {
            Map<String, Object> eventData = new HashMap<>();
            eventData.put("product_id", productId);
            eventData.put("product_name", productName);
            eventData.put("user_id", userId);
            eventData.put("quantity", quantity);
            eventData.put("timestamp", System.currentTimeMillis());
            
            log.info("Cart addition tracked: productId={}, quantity={}, userId={}", productId, quantity, userId);
        } catch (Exception e) {
            log.error("Failed to track cart addition", e);
        }
    }

    public void trackSearchQuery(String searchQuery, Long userId, Integer resultsCount) {
        if (firebaseApp == null) {
            log.debug("Firebase not available, skipping search tracking");
            return;
        }

        try {
            Map<String, Object> eventData = new HashMap<>();
            eventData.put("search_query", searchQuery);
            eventData.put("user_id", userId);
            eventData.put("results_count", resultsCount);
            eventData.put("timestamp", System.currentTimeMillis());
            
            log.info("Search query tracked: query={}, userId={}, results={}", searchQuery, userId, resultsCount);
        } catch (Exception e) {
            log.error("Failed to track search query", e);
        }
    }

    public void trackUserLogin(Long userId, String loginMethod) {
        if (firebaseApp == null) {
            log.debug("Firebase not available, skipping login tracking");
            return;
        }

        try {
            Map<String, Object> eventData = new HashMap<>();
            eventData.put("user_id", userId);
            eventData.put("login_method", loginMethod);
            eventData.put("timestamp", System.currentTimeMillis());
            
            log.info("User login tracked: userId={}, method={}", userId, loginMethod);
        } catch (Exception e) {
            log.error("Failed to track user login", e);
        }
    }

    public boolean isFirebaseAvailable() {
        return firebaseApp != null;
    }
}
