@echo off
set PGPASSWORD=admin
echo Creating database grocease_db...
"C:\Program Files\PostgreSQL\17\bin\psql.exe" -U postgres -h localhost -p 5432 -c "CREATE DATABASE grocease_db;" 2>nul
echo Creating user grocease_user...
"C:\Program Files\PostgreSQL\17\bin\psql.exe" -U postgres -h localhost -p 5432 -c "CREATE USER grocease_user WITH PASSWORD 'admin';" 2>nul
echo Granting privileges...
"C:\Program Files\PostgreSQL\17\bin\psql.exe" -U postgres -h localhost -p 5432 -c "GRANT ALL PRIVILEGES ON DATABASE grocease_db TO grocease_user;"
echo Database setup completed.
pause
