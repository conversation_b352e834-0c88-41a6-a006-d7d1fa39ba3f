package com.grocease.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "app")
public class AppProperties {

    private Cors cors = new Cors();
    private Security security = new Security();
    private Features features = new Features();

    @Data
    public static class Cors {
        private String allowedOrigins = "http://localhost:3000,http://localhost:19006,http://localhost:8081";
    }

    @Data
    public static class Security {
        private Jwt jwt = new Jwt();

        @Data
        public static class Jwt {
            private String header = "Authorization";
            private String prefix = "Bearer ";
        }
    }

    @Data
    public static class Features {
        private EmailVerification emailVerification = new EmailVerification();

        @Data
        public static class EmailVerification {
            private boolean enabled = false;  // Disabled by default for development
        }
    }
}
