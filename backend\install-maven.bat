@echo off
echo Installing Maven for GrocEase Backend...
echo =====================================

REM Create maven directory
if not exist "C:\maven" mkdir "C:\maven"
cd "C:\maven"

echo Downloading Maven 3.9.6...
powershell -Command "Invoke-WebRequest -Uri 'https://archive.apache.org/dist/maven/maven-3/3.9.6/binaries/apache-maven-3.9.6-bin.zip' -OutFile 'maven.zip'"

echo Extracting Maven...
powershell -Command "Expand-Archive -Path 'maven.zip' -DestinationPath '.'"

echo Setting up Maven...
set MAVEN_HOME=C:\maven\apache-maven-3.9.6
set PATH=%MAVEN_HOME%\bin;%PATH%

echo Testing Maven installation...
"%MAVEN_HOME%\bin\mvn.cmd" -version

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ Maven installed successfully!
    echo.
    echo To use Maven permanently, add these to your system environment variables:
    echo MAVEN_HOME=C:\maven\apache-maven-3.9.6
    echo PATH=%PATH%;%%MAVEN_HOME%%\bin
    echo.
    echo Now you can run: mvn spring-boot:run
) else (
    echo ❌ Maven installation failed
)

pause
