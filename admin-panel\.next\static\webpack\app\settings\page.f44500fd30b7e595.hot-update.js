"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/settings/page",{

/***/ "(app-pages-browser)/./src/app/settings/page.tsx":
/*!***********************************!*\
  !*** ./src/app/settings/page.tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SettingsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _store_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/auth */ \"(app-pages-browser)/./src/store/auth.ts\");\n/* harmony import */ var _components_layout_AdminLayout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/layout/AdminLayout */ \"(app-pages-browser)/./src/components/layout/AdminLayout.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_switch__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/switch */ \"(app-pages-browser)/./src/components/ui/switch.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./src/components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_image_upload__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/image-upload */ \"(app-pages-browser)/./src/components/ui/image-upload.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bell_Database_Save_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Database,Save,Shield,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Database_Save_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Database,Save,Shield,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Database_Save_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Database,Save,Shield,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Database_Save_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Database,Save,Shield,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Database_Save_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Database,Save,Shield,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction SettingsPage() {\n    _s();\n    const { user } = (0,_store_auth__WEBPACK_IMPORTED_MODULE_2__.useAuthStore)();\n    const [profileData, setProfileData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: (user === null || user === void 0 ? void 0 : user.name) || \"\",\n        email: (user === null || user === void 0 ? void 0 : user.email) || \"\",\n        phone: (user === null || user === void 0 ? void 0 : user.phone) || \"\",\n        avatar: (user === null || user === void 0 ? void 0 : user.avatar) || \"\"\n    });\n    const [notificationSettings, setNotificationSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        emailNotifications: true,\n        pushNotifications: true,\n        orderUpdates: true,\n        marketingEmails: false,\n        weeklyReports: true\n    });\n    const [systemSettings, setSystemSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        maintenanceMode: false,\n        allowRegistrations: true,\n        requireEmailVerification: true,\n        enableAnalytics: true,\n        autoBackup: true\n    });\n    const handleProfileSave = ()=>{\n        // Implementation for saving profile\n        console.log(\"Saving profile:\", profileData);\n    };\n    const handleNotificationSave = ()=>{\n        // Implementation for saving notification settings\n        console.log(\"Saving notification settings:\", notificationSettings);\n    };\n    const handleSystemSave = ()=>{\n        // Implementation for saving system settings\n        console.log(\"Saving system settings:\", systemSettings);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_AdminLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold tracking-tight\",\n                            children: \"Settings\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground\",\n                            children: \"Manage your account and system preferences\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.Tabs, {\n                    defaultValue: \"profile\",\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsList, {\n                            className: \"grid w-full grid-cols-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsTrigger, {\n                                    value: \"profile\",\n                                    children: \"Profile\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsTrigger, {\n                                    value: \"notifications\",\n                                    children: \"Notifications\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsTrigger, {\n                                    value: \"security\",\n                                    children: \"Security\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsTrigger, {\n                                    value: \"system\",\n                                    children: \"System\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsContent, {\n                            value: \"profile\",\n                            className: \"space-y-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Database_Save_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"h-5 w-5 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 90,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Profile Information\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 89,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                                children: \"Update your personal information and profile picture\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 93,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                        className: \"text-base font-medium\",\n                                                        children: \"Profile Picture\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 99,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-muted-foreground mb-4\",\n                                                        children: \"Upload a new avatar or profile picture\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 100,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_image_upload__WEBPACK_IMPORTED_MODULE_11__.ImageUpload, {\n                                                        value: profileData.avatar,\n                                                        onChange: (url)=>setProfileData({\n                                                                ...profileData,\n                                                                avatar: url\n                                                            }),\n                                                        type: \"avatar\",\n                                                        preview: \"avatar\",\n                                                        placeholder: \"Upload your profile picture\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 103,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 98,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_8__.Separator, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 112,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid gap-4 md:grid-cols-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                htmlFor: \"name\",\n                                                                children: \"Full Name\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 116,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                id: \"name\",\n                                                                value: profileData.name,\n                                                                onChange: (e)=>setProfileData({\n                                                                        ...profileData,\n                                                                        name: e.target.value\n                                                                    })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 117,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 115,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                htmlFor: \"email\",\n                                                                children: \"Email\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 124,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                id: \"email\",\n                                                                type: \"email\",\n                                                                value: profileData.email,\n                                                                onChange: (e)=>setProfileData({\n                                                                        ...profileData,\n                                                                        email: e.target.value\n                                                                    })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 125,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 123,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                htmlFor: \"phone\",\n                                                                children: \"Phone\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 133,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                id: \"phone\",\n                                                                value: profileData.phone,\n                                                                onChange: (e)=>setProfileData({\n                                                                        ...profileData,\n                                                                        phone: e.target.value\n                                                                    })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 134,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 132,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 114,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                onClick: handleProfileSave,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Database_Save_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 143,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Save Changes\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 87,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsContent, {\n                            value: \"notifications\",\n                            className: \"space-y-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Database_Save_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"h-5 w-5 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 155,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Notification Preferences\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 154,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                                children: \"Configure how you want to receive notifications\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-0.5\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                        children: \"Email Notifications\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 166,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: \"Receive notifications via email\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 167,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 165,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_9__.Switch, {\n                                                                checked: notificationSettings.emailNotifications,\n                                                                onCheckedChange: (checked)=>setNotificationSettings({\n                                                                        ...notificationSettings,\n                                                                        emailNotifications: checked\n                                                                    })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 171,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 164,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-0.5\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                        children: \"Push Notifications\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 181,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: \"Receive push notifications in browser\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 182,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 180,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_9__.Switch, {\n                                                                checked: notificationSettings.pushNotifications,\n                                                                onCheckedChange: (checked)=>setNotificationSettings({\n                                                                        ...notificationSettings,\n                                                                        pushNotifications: checked\n                                                                    })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 186,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 179,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-0.5\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                        children: \"Order Updates\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 196,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: \"Get notified about order status changes\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 197,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 195,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_9__.Switch, {\n                                                                checked: notificationSettings.orderUpdates,\n                                                                onCheckedChange: (checked)=>setNotificationSettings({\n                                                                        ...notificationSettings,\n                                                                        orderUpdates: checked\n                                                                    })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 201,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 194,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-0.5\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                        children: \"Marketing Emails\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 211,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: \"Receive promotional and marketing emails\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 212,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 210,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_9__.Switch, {\n                                                                checked: notificationSettings.marketingEmails,\n                                                                onCheckedChange: (checked)=>setNotificationSettings({\n                                                                        ...notificationSettings,\n                                                                        marketingEmails: checked\n                                                                    })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 216,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 209,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-0.5\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                        children: \"Weekly Reports\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 226,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: \"Receive weekly analytics reports\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 227,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 225,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_9__.Switch, {\n                                                                checked: notificationSettings.weeklyReports,\n                                                                onCheckedChange: (checked)=>setNotificationSettings({\n                                                                        ...notificationSettings,\n                                                                        weeklyReports: checked\n                                                                    })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 231,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 224,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                onClick: handleNotificationSave,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Database_Save_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 241,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Save Preferences\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 240,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsContent, {\n                            value: \"security\",\n                            className: \"space-y-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Database_Save_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"h-5 w-5 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 253,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Security Settings\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 252,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                                children: \"Manage your account security and authentication\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium mb-4\",\n                                                        children: \"Change Password\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 262,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                        htmlFor: \"current-password\",\n                                                                        children: \"Current Password\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 265,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                        id: \"current-password\",\n                                                                        type: \"password\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 266,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 264,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                        htmlFor: \"new-password\",\n                                                                        children: \"New Password\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 269,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                        id: \"new-password\",\n                                                                        type: \"password\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 270,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 268,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                        htmlFor: \"confirm-password\",\n                                                                        children: \"Confirm New Password\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 273,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                        id: \"confirm-password\",\n                                                                        type: \"password\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 274,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 272,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                className: \"w-fit\",\n                                                                children: \"Update Password\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 276,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 263,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_8__.Separator, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium mb-4\",\n                                                        children: \"Two-Factor Authentication\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 283,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm\",\n                                                                        children: \"Enable two-factor authentication for enhanced security\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 286,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: \"Currently disabled\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 287,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 285,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                variant: \"outline\",\n                                                                children: \"Enable 2FA\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 291,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 284,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 282,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_8__.Separator, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium mb-4\",\n                                                        children: \"Active Sessions\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 298,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between p-3 border rounded-lg\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium\",\n                                                                            children: \"Current Session\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 302,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-muted-foreground\",\n                                                                            children: \"Chrome on Windows • Active now\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 303,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 301,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_12__.Badge, {\n                                                                    variant: \"default\",\n                                                                    children: \"Current\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 305,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 300,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 299,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 248,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsContent, {\n                            value: \"system\",\n                            className: \"space-y-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Database_Save_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"h-5 w-5 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 318,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"System Configuration\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 317,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                                children: \"Configure system-wide settings and preferences\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 321,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 316,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-0.5\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                        children: \"Maintenance Mode\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 329,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: \"Put the system in maintenance mode\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 330,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 328,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_9__.Switch, {\n                                                                checked: systemSettings.maintenanceMode,\n                                                                onCheckedChange: (checked)=>setSystemSettings({\n                                                                        ...systemSettings,\n                                                                        maintenanceMode: checked\n                                                                    })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 334,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 327,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-0.5\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                        children: \"Allow User Registrations\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 344,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: \"Allow new users to register accounts\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 345,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 343,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_9__.Switch, {\n                                                                checked: systemSettings.allowRegistrations,\n                                                                onCheckedChange: (checked)=>setSystemSettings({\n                                                                        ...systemSettings,\n                                                                        allowRegistrations: checked\n                                                                    })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 349,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 342,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-0.5\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                        children: \"Require Email Verification\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 359,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: \"Require users to verify their email addresses\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 360,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 358,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_9__.Switch, {\n                                                                checked: systemSettings.requireEmailVerification,\n                                                                onCheckedChange: (checked)=>setSystemSettings({\n                                                                        ...systemSettings,\n                                                                        requireEmailVerification: checked\n                                                                    })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 364,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 357,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-0.5\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                        children: \"Enable Analytics\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 374,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: \"Collect analytics data for insights\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 375,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 373,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_9__.Switch, {\n                                                                checked: systemSettings.enableAnalytics,\n                                                                onCheckedChange: (checked)=>setSystemSettings({\n                                                                        ...systemSettings,\n                                                                        enableAnalytics: checked\n                                                                    })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 379,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 372,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-0.5\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                        children: \"Automatic Backups\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 389,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: \"Enable automatic daily database backups\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 390,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 388,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_9__.Switch, {\n                                                                checked: systemSettings.autoBackup,\n                                                                onCheckedChange: (checked)=>setSystemSettings({\n                                                                        ...systemSettings,\n                                                                        autoBackup: checked\n                                                                    })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 394,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 387,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 326,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                onClick: handleSystemSave,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Database_Save_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 404,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Save System Settings\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 403,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 313,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n            lineNumber: 68,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n        lineNumber: 67,\n        columnNumber: 5\n    }, this);\n}\n_s(SettingsPage, \"ddqVEIcxOZ0VL3C4mOCIihM9eJw=\", false, function() {\n    return [\n        _store_auth__WEBPACK_IMPORTED_MODULE_2__.useAuthStore\n    ];\n});\n_c = SettingsPage;\nvar _c;\n$RefreshReg$(_c, \"SettingsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/settings/page.tsx\n"));

/***/ })

});