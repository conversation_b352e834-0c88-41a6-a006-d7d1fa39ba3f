-- Test script to check and clean up placeholder URLs in the database
-- Run this after fixing the Cloudinary configuration

-- Check for placeholder URLs in users table
SELECT id, name, email, avatar 
FROM users 
WHERE avatar LIKE '%placeholder%';

-- Check for placeholder URLs in products table
SELECT id, name, image 
FROM products 
WHERE image LIKE '%placeholder%';

-- Check for placeholder URLs in categories table
SELECT id, name, image 
FROM categories 
WHERE image LIKE '%placeholder%';

-- Check for placeholder URLs in banners table
SELECT id, title, image 
FROM banners 
WHERE image LIKE '%placeholder%';

-- Optional: Clean up placeholder URLs (uncomment if needed)
-- UPDATE users SET avatar = NULL WHERE avatar LIKE '%placeholder%';
-- UPDATE products SET image = NULL WHERE image LIKE '%placeholder%';
-- UPDATE categories SET image = NULL WHERE image LIKE '%placeholder%';
-- UPDATE banners SET image = NULL WHERE image LIKE '%placeholder%';
