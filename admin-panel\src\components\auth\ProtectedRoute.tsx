'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuthStore } from '@/store/auth'
import { Loader2 } from 'lucide-react'

interface ProtectedRouteProps {
  children: React.ReactNode
}

export default function ProtectedRoute({ children }: ProtectedRouteProps) {
  const { isAuthenticated, isLoading, user, hasHydrated, initializeAuth } = useAuthStore()
  const router = useRouter()

  useEffect(() => {
    // Initialize auth state on client side
    if (!hasHydrated) {
      initializeAuth()
    }
  }, [hasHydrated, initializeAuth])

  useEffect(() => {
    // Only redirect after hydration is complete
    if (!hasHydrated) return

    // Check if user is authenticated and has admin role
    if (!isLoading && (!isAuthenticated || !user)) {
      router.push('/login')
      return
    }

    // Additional check for admin role if user data is available
    if (user && user.role !== 'ADMIN') {
      router.push('/login')
      return
    }
  }, [isAuthenticated, isLoading, user, router, hasHydrated])

  // Show loading while hydrating or loading
  if (!hasHydrated || isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="flex items-center space-x-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>Loading...</span>
        </div>
      </div>
    )
  }

  if (!isAuthenticated || !user) {
    return null
  }

  return <>{children}</>
}
