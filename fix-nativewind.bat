@echo off
echo 🔧 Fixing NativeWind Babel Error...

REM Remove node_modules and package-lock.json
echo 📦 Cleaning dependencies...
if exist node_modules rmdir /s /q node_modules
if exist package-lock.json del package-lock.json
if exist yarn.lock del yarn.lock

REM Remove Metro cache
echo 🧹 Clearing Metro cache...
if exist .metro rmdir /s /q .metro

REM Install dependencies
echo 📥 Installing dependencies...
npm install

REM Clear Expo cache
echo 🧹 Clearing Expo cache...
npx expo start --clear

echo ✅ NativeWind v2 configuration complete!
echo 🚀 Your app should now start without Babel errors.
pause
