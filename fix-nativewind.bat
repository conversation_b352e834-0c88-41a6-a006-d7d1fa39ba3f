@echo off
echo 🔧 Fixing NativeWind PostCSS Error...

REM Remove node_modules and package-lock.json
echo 📦 Cleaning dependencies...
if exist node_modules rmdir /s /q node_modules
if exist package-lock.json del package-lock.json
if exist yarn.lock del yarn.lock

REM Install dependencies
echo 📥 Installing updated dependencies...
npm install

REM Clear Metro cache
echo 🧹 Clearing Metro cache...
npx expo start --clear

echo ✅ NativeWind v4 upgrade complete!
echo 🚀 Your app should now start without PostCSS errors.
pause
