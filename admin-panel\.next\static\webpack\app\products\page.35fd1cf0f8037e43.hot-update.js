"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/products/page",{

/***/ "(app-pages-browser)/./src/app/products/page.tsx":
/*!***********************************!*\
  !*** ./src/app/products/page.tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ProductsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useMutation.js\");\n/* harmony import */ var _components_layout_AdminLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/AdminLayout */ \"(app-pages-browser)/./src/components/layout/AdminLayout.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_switch__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/switch */ \"(app-pages-browser)/./src/components/ui/switch.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_image_upload__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/image-upload */ \"(app-pages-browser)/./src/components/ui/image-upload.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./src/components/ui/table.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Edit_Eye_Package_Plus_Search_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Edit,Eye,Package,Plus,Search,Star,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Edit_Eye_Package_Plus_Search_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Edit,Eye,Package,Plus,Search,Star,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Edit_Eye_Package_Plus_Search_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Edit,Eye,Package,Plus,Search,Star,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Edit_Eye_Package_Plus_Search_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Edit,Eye,Package,Plus,Search,Star,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Edit_Eye_Package_Plus_Search_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Edit,Eye,Package,Plus,Search,Star,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Edit_Eye_Package_Plus_Search_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Edit,Eye,Package,Plus,Search,Star,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen-square.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Edit_Eye_Package_Plus_Search_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Edit,Eye,Package,Plus,Search,Star,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Edit_Eye_Package_Plus_Search_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Edit,Eye,Package,Plus,Search,Star,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Edit_Eye_Package_Plus_Search_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Edit,Eye,Package,Plus,Search,Star,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_17___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_17__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_18___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_18__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction ProductsPage() {\n    _s();\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [search, setSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [categoryFilter, setCategoryFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"ALL\");\n    const [isCreateDialogOpen, setIsCreateDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isEditDialogOpen, setIsEditDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingProduct, setEditingProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        description: \"\",\n        price: 0,\n        originalPrice: 0,\n        discount: 0,\n        image: \"\",\n        categoryId: 0,\n        unit: \"\",\n        inStock: true,\n        rating: 0,\n        reviewCount: 0,\n        tags: []\n    });\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_19__.useQueryClient)();\n    const { data: productsData, isLoading } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_20__.useQuery)({\n        queryKey: [\n            \"products\",\n            page,\n            search,\n            categoryFilter\n        ],\n        queryFn: ()=>_lib_api__WEBPACK_IMPORTED_MODULE_14__[\"default\"].getProducts(page, 10, categoryFilter === \"ALL\" ? undefined : categoryFilter, search || undefined)\n    });\n    const { data: categories } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_20__.useQuery)({\n        queryKey: [\n            \"categories\"\n        ],\n        queryFn: ()=>_lib_api__WEBPACK_IMPORTED_MODULE_14__[\"default\"].getCategories()\n    });\n    const createMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_21__.useMutation)({\n        mutationFn: (data)=>_lib_api__WEBPACK_IMPORTED_MODULE_14__[\"default\"].createProduct(data),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"products\"\n                ]\n            });\n            setIsCreateDialogOpen(false);\n            resetForm();\n            sonner__WEBPACK_IMPORTED_MODULE_16__.toast.success(\"Product created successfully\");\n        },\n        onError: (error)=>{\n            var _error_response_data, _error_response;\n            sonner__WEBPACK_IMPORTED_MODULE_16__.toast.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"Failed to create product\");\n        }\n    });\n    const updateMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_21__.useMutation)({\n        mutationFn: (param)=>{\n            let { id, data } = param;\n            return _lib_api__WEBPACK_IMPORTED_MODULE_14__[\"default\"].updateProduct(id, data);\n        },\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"products\"\n                ]\n            });\n            setIsEditDialogOpen(false);\n            setEditingProduct(null);\n            resetForm();\n            sonner__WEBPACK_IMPORTED_MODULE_16__.toast.success(\"Product updated successfully\");\n        },\n        onError: (error)=>{\n            var _error_response_data, _error_response;\n            sonner__WEBPACK_IMPORTED_MODULE_16__.toast.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"Failed to update product\");\n        }\n    });\n    const deleteMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_21__.useMutation)({\n        mutationFn: (id)=>_lib_api__WEBPACK_IMPORTED_MODULE_14__[\"default\"].deleteProduct(id),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"products\"\n                ]\n            });\n            sonner__WEBPACK_IMPORTED_MODULE_16__.toast.success(\"Product deleted successfully\");\n        },\n        onError: (error)=>{\n            var _error_response_data, _error_response;\n            sonner__WEBPACK_IMPORTED_MODULE_16__.toast.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"Failed to delete product\");\n        }\n    });\n    const resetForm = ()=>{\n        setFormData({\n            name: \"\",\n            description: \"\",\n            price: 0,\n            originalPrice: 0,\n            discount: 0,\n            image: \"\",\n            categoryId: 0,\n            unit: \"\",\n            inStock: true,\n            rating: 0,\n            reviewCount: 0,\n            tags: []\n        });\n    };\n    const handleCreate = ()=>{\n        createMutation.mutate(formData);\n    };\n    const handleEdit = (product)=>{\n        var _categories_find;\n        setEditingProduct(product);\n        // Find the category ID from the category name\n        const categoryId = (categories === null || categories === void 0 ? void 0 : (_categories_find = categories.find((cat)=>cat.name === product.category)) === null || _categories_find === void 0 ? void 0 : _categories_find.id) || \"0\";\n        setFormData({\n            name: product.name,\n            description: product.description,\n            price: product.price,\n            originalPrice: product.originalPrice || 0,\n            discount: product.discount || 0,\n            image: product.image,\n            categoryId: parseInt(categoryId),\n            unit: product.unit,\n            inStock: product.inStock,\n            rating: product.rating,\n            reviewCount: product.reviewCount,\n            tags: product.tags || []\n        });\n        setIsEditDialogOpen(true);\n    };\n    const handleUpdate = ()=>{\n        if (editingProduct) {\n            updateMutation.mutate({\n                id: editingProduct.id,\n                data: formData\n            });\n        }\n    };\n    const handleDelete = (id)=>{\n        if (confirm(\"Are you sure you want to delete this product?\")) {\n            deleteMutation.mutate(id);\n        }\n    };\n    const filteredProducts = (productsData === null || productsData === void 0 ? void 0 : productsData.data) || [];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_AdminLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold tracking-tight\",\n                                    children: \"Products\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-muted-foreground\",\n                                    children: \"Manage your product catalog\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                            lineNumber: 197,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_13__.Dialog, {\n                            open: isCreateDialogOpen,\n                            onOpenChange: setIsCreateDialogOpen,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_13__.DialogTrigger, {\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: resetForm,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Edit_Eye_Package_Plus_Search_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Add Product\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                            lineNumber: 203,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                    lineNumber: 196,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    children: \"Filter Products\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                    children: \"Search and filter products by category\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Edit_Eye_Package_Plus_Search_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                    className: \"absolute left-3 top-3 h-4 w-4 text-muted-foreground\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                    lineNumber: 223,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                    placeholder: \"Search products...\",\n                                                    value: search,\n                                                    onChange: (e)=>setSearch(e.target.value),\n                                                    className: \"pl-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                    lineNumber: 224,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                            lineNumber: 222,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.Select, {\n                                        value: categoryFilter,\n                                        onValueChange: setCategoryFilter,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectTrigger, {\n                                                className: \"w-48\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectValue, {\n                                                    placeholder: \"Filter by category\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                    lineNumber: 234,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectContent, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectItem, {\n                                                        value: \"ALL\",\n                                                        children: \"All Categories\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 237,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    categories === null || categories === void 0 ? void 0 : categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectItem, {\n                                                            value: category.id,\n                                                            children: category.name\n                                                        }, category.id, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                            lineNumber: 239,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                            lineNumber: 219,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                    lineNumber: 214,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    children: \"Products List\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                    children: [\n                                        (productsData === null || productsData === void 0 ? void 0 : productsData.pagination.total) || 0,\n                                        \" total products\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                    lineNumber: 253,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                            lineNumber: 251,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center h-32\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                lineNumber: 259,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.Table, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHeader, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableRow, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHead, {\n                                                            children: \"Product\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                            lineNumber: 267,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHead, {\n                                                            children: \"Category\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                            lineNumber: 268,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHead, {\n                                                            children: \"Price\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                            lineNumber: 269,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHead, {\n                                                            children: \"Stock\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                            lineNumber: 270,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHead, {\n                                                            children: \"Rating\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                            lineNumber: 271,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHead, {\n                                                            children: \"Status\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                            lineNumber: 272,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHead, {\n                                                            children: \"Actions\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                            lineNumber: 273,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                    lineNumber: 266,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableBody, {\n                                                children: filteredProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableRow, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableCell, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex-shrink-0\",\n                                                                            children: product.image ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_17___default()), {\n                                                                                src: product.image,\n                                                                                alt: product.name,\n                                                                                width: 50,\n                                                                                height: 50,\n                                                                                className: \"rounded-lg object-cover\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                                lineNumber: 283,\n                                                                                columnNumber: 33\n                                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-12 h-12 bg-muted rounded-lg flex items-center justify-center\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Edit_Eye_Package_Plus_Search_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                    className: \"h-6 w-6 text-muted-foreground\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                                    lineNumber: 292,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                                lineNumber: 291,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 281,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"font-medium\",\n                                                                                    children: product.name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                                    lineNumber: 297,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm text-muted-foreground\",\n                                                                                    children: product.unit\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                                    lineNumber: 298,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 296,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 280,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                lineNumber: 279,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableCell, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    variant: \"outline\",\n                                                                    children: product.category\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 305,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                lineNumber: 304,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableCell, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium\",\n                                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.formatCurrency)(product.price)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 309,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        product.originalPrice && product.originalPrice > product.price && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-muted-foreground line-through\",\n                                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.formatCurrency)(product.originalPrice)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 311,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 308,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                lineNumber: 307,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableCell, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-medium \".concat(!product.inStock ? \"text-red-600\" : \"\"),\n                                                                        children: product.inStock ? \"In Stock\" : \"Out of Stock\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                        lineNumber: 319,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 318,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                lineNumber: 317,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableCell, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Edit_Eye_Package_Plus_Search_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                            className: \"h-4 w-4 fill-yellow-400 text-yellow-400\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 326,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium\",\n                                                                            children: product.rating\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 327,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-muted-foreground\",\n                                                                            children: [\n                                                                                \"(\",\n                                                                                (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.formatNumber)(product.reviewCount),\n                                                                                \")\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 328,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 325,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                lineNumber: 324,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableCell, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    variant: product.inStock ? \"default\" : \"destructive\",\n                                                                    children: product.inStock ? \"Active\" : \"Inactive\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 334,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                lineNumber: 333,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableCell, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_18___default()), {\n                                                                            href: \"/products/\".concat(product.id),\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                variant: \"outline\",\n                                                                                size: \"sm\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Edit_Eye_Package_Plus_Search_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                    className: \"h-4 w-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                                    lineNumber: 344,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                                lineNumber: 343,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 342,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                            variant: \"outline\",\n                                                                            size: \"sm\",\n                                                                            onClick: ()=>handleEdit(product),\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Edit_Eye_Package_Plus_Search_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                                lineNumber: 352,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 347,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                            variant: \"outline\",\n                                                                            size: \"sm\",\n                                                                            onClick: ()=>handleDelete(product.id),\n                                                                            disabled: deleteMutation.isPending,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Edit_Eye_Package_Plus_Search_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                                lineNumber: 360,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 354,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 341,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                lineNumber: 340,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, product.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 278,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 264,\n                                        columnNumber: 17\n                                    }, this),\n                                    (productsData === null || productsData === void 0 ? void 0 : productsData.pagination) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mt-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: [\n                                                    \"Showing \",\n                                                    page * 10 + 1,\n                                                    \" to \",\n                                                    Math.min((page + 1) * 10, productsData.pagination.total),\n                                                    \" of\",\n                                                    \" \",\n                                                    productsData.pagination.total,\n                                                    \" products\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 372,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        onClick: ()=>setPage(page - 1),\n                                                        disabled: page === 0,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Edit_Eye_Package_Plus_Search_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                lineNumber: 383,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Previous\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 377,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        onClick: ()=>setPage(page + 1),\n                                                        disabled: page >= productsData.pagination.totalPages - 1,\n                                                        children: [\n                                                            \"Next\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Edit_Eye_Package_Plus_Search_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                lineNumber: 393,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 386,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 376,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 371,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                            lineNumber: 257,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                    lineNumber: 250,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_13__.Dialog, {\n                    open: isCreateDialogOpen,\n                    onOpenChange: setIsCreateDialogOpen,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_13__.DialogContent, {\n                        className: \"sm:max-w-[600px]\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_13__.DialogHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_13__.DialogTitle, {\n                                        children: \"Create Product\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 407,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_13__.DialogDescription, {\n                                        children: \"Add a new product to your catalog.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 408,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                lineNumber: 406,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid gap-4 py-4 max-h-[60vh] overflow-y-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                htmlFor: \"name\",\n                                                children: \"Product Name\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 414,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                id: \"name\",\n                                                value: formData.name,\n                                                onChange: (e)=>setFormData({\n                                                        ...formData,\n                                                        name: e.target.value\n                                                    }),\n                                                placeholder: \"Product name\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 415,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 413,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                htmlFor: \"description\",\n                                                children: \"Description\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 423,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_9__.Textarea, {\n                                                id: \"description\",\n                                                value: formData.description,\n                                                onChange: (e)=>setFormData({\n                                                        ...formData,\n                                                        description: e.target.value\n                                                    }),\n                                                placeholder: \"Product description\",\n                                                rows: 3\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 424,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 422,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                        htmlFor: \"price\",\n                                                        children: \"Price\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 434,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                        id: \"price\",\n                                                        type: \"number\",\n                                                        step: \"0.01\",\n                                                        value: formData.price,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                price: parseFloat(e.target.value) || 0\n                                                            }),\n                                                        placeholder: \"0.00\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 435,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 433,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                        htmlFor: \"originalPrice\",\n                                                        children: \"Original Price\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 445,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                        id: \"originalPrice\",\n                                                        type: \"number\",\n                                                        step: \"0.01\",\n                                                        value: formData.originalPrice,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                originalPrice: parseFloat(e.target.value) || 0\n                                                            }),\n                                                        placeholder: \"0.00\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 446,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 444,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 432,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                        htmlFor: \"discount\",\n                                                        children: \"Discount (%)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 458,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                        id: \"discount\",\n                                                        type: \"number\",\n                                                        step: \"0.01\",\n                                                        value: formData.discount,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                discount: parseFloat(e.target.value) || 0\n                                                            }),\n                                                        placeholder: \"0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 459,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 457,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                        htmlFor: \"unit\",\n                                                        children: \"Unit\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 469,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                        id: \"unit\",\n                                                        value: formData.unit,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                unit: e.target.value\n                                                            }),\n                                                        placeholder: \"kg, piece, liter, etc.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 470,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 468,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 456,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                htmlFor: \"category\",\n                                                children: \"Category\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 479,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.Select, {\n                                                value: formData.categoryId.toString(),\n                                                onValueChange: (value)=>setFormData({\n                                                        ...formData,\n                                                        categoryId: parseInt(value)\n                                                    }),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectTrigger, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectValue, {\n                                                            placeholder: \"Select a category\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                            lineNumber: 485,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 484,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectContent, {\n                                                        children: categories === null || categories === void 0 ? void 0 : categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectItem, {\n                                                                value: category.id,\n                                                                children: category.name\n                                                            }, category.id, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                lineNumber: 489,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 487,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 480,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 478,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                children: \"Product Image\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 497,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_image_upload__WEBPACK_IMPORTED_MODULE_10__.ImageUpload, {\n                                                value: formData.image,\n                                                onChange: (url)=>setFormData({\n                                                        ...formData,\n                                                        image: url\n                                                    }),\n                                                type: \"product\",\n                                                placeholder: \"Upload product image\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 498,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 496,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                        htmlFor: \"rating\",\n                                                        children: \"Rating\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 507,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                        id: \"rating\",\n                                                        type: \"number\",\n                                                        step: \"0.1\",\n                                                        min: \"0\",\n                                                        max: \"5\",\n                                                        value: formData.rating,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                rating: parseFloat(e.target.value) || 0\n                                                            }),\n                                                        placeholder: \"0.0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 508,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 506,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                        htmlFor: \"reviewCount\",\n                                                        children: \"Review Count\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 520,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                        id: \"reviewCount\",\n                                                        type: \"number\",\n                                                        value: formData.reviewCount,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                reviewCount: parseInt(e.target.value) || 0\n                                                            }),\n                                                        placeholder: \"0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 521,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 519,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 505,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_8__.Switch, {\n                                                id: \"inStock\",\n                                                checked: formData.inStock,\n                                                onCheckedChange: (checked)=>setFormData({\n                                                        ...formData,\n                                                        inStock: checked\n                                                    })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 531,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                htmlFor: \"inStock\",\n                                                children: \"In Stock\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 536,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 530,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                lineNumber: 412,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_13__.DialogFooter, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: handleCreate,\n                                    disabled: createMutation.isPending || !formData.name.trim() || !formData.categoryId,\n                                    children: createMutation.isPending ? \"Creating...\" : \"Create Product\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                    lineNumber: 540,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                lineNumber: 539,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                        lineNumber: 405,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                    lineNumber: 404,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_13__.Dialog, {\n                    open: isEditDialogOpen,\n                    onOpenChange: setIsEditDialogOpen,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_13__.DialogContent, {\n                        className: \"sm:max-w-[600px]\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_13__.DialogHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_13__.DialogTitle, {\n                                        children: \"Edit Product\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 554,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_13__.DialogDescription, {\n                                        children: \"Update the product information.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 555,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                lineNumber: 553,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid gap-4 py-4 max-h-[60vh] overflow-y-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                htmlFor: \"edit-name\",\n                                                children: \"Product Name\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 561,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                id: \"edit-name\",\n                                                value: formData.name,\n                                                onChange: (e)=>setFormData({\n                                                        ...formData,\n                                                        name: e.target.value\n                                                    }),\n                                                placeholder: \"Product name\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 562,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 560,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                htmlFor: \"edit-description\",\n                                                children: \"Description\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 570,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_9__.Textarea, {\n                                                id: \"edit-description\",\n                                                value: formData.description,\n                                                onChange: (e)=>setFormData({\n                                                        ...formData,\n                                                        description: e.target.value\n                                                    }),\n                                                placeholder: \"Product description\",\n                                                rows: 3\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 571,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 569,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                        htmlFor: \"edit-price\",\n                                                        children: \"Price\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 581,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                        id: \"edit-price\",\n                                                        type: \"number\",\n                                                        step: \"0.01\",\n                                                        value: formData.price,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                price: parseFloat(e.target.value) || 0\n                                                            }),\n                                                        placeholder: \"0.00\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 582,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 580,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                        htmlFor: \"edit-originalPrice\",\n                                                        children: \"Original Price\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 592,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                        id: \"edit-originalPrice\",\n                                                        type: \"number\",\n                                                        step: \"0.01\",\n                                                        value: formData.originalPrice,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                originalPrice: parseFloat(e.target.value) || 0\n                                                            }),\n                                                        placeholder: \"0.00\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 593,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 591,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 579,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                        htmlFor: \"edit-discount\",\n                                                        children: \"Discount (%)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 605,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                        id: \"edit-discount\",\n                                                        type: \"number\",\n                                                        step: \"0.01\",\n                                                        value: formData.discount,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                discount: parseFloat(e.target.value) || 0\n                                                            }),\n                                                        placeholder: \"0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 606,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 604,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                        htmlFor: \"edit-unit\",\n                                                        children: \"Unit\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 616,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                        id: \"edit-unit\",\n                                                        value: formData.unit,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                unit: e.target.value\n                                                            }),\n                                                        placeholder: \"kg, piece, liter, etc.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 617,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 615,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 603,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                htmlFor: \"edit-category\",\n                                                children: \"Category\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 626,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.Select, {\n                                                value: formData.categoryId.toString(),\n                                                onValueChange: (value)=>setFormData({\n                                                        ...formData,\n                                                        categoryId: parseInt(value)\n                                                    }),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectTrigger, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectValue, {\n                                                            placeholder: \"Select a category\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                            lineNumber: 632,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 631,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectContent, {\n                                                        children: categories === null || categories === void 0 ? void 0 : categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectItem, {\n                                                                value: category.id,\n                                                                children: category.name\n                                                            }, category.id, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                lineNumber: 636,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 634,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 627,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 625,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                children: \"Product Image\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 644,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_image_upload__WEBPACK_IMPORTED_MODULE_10__.ImageUpload, {\n                                                value: formData.image,\n                                                onChange: (url)=>setFormData({\n                                                        ...formData,\n                                                        image: url\n                                                    }),\n                                                type: \"product\",\n                                                placeholder: \"Upload product image\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 645,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 643,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                        htmlFor: \"edit-rating\",\n                                                        children: \"Rating\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 654,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                        id: \"edit-rating\",\n                                                        type: \"number\",\n                                                        step: \"0.1\",\n                                                        min: \"0\",\n                                                        max: \"5\",\n                                                        value: formData.rating,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                rating: parseFloat(e.target.value) || 0\n                                                            }),\n                                                        placeholder: \"0.0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 655,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 653,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                        htmlFor: \"edit-reviewCount\",\n                                                        children: \"Review Count\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 667,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                        id: \"edit-reviewCount\",\n                                                        type: \"number\",\n                                                        value: formData.reviewCount,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                reviewCount: parseInt(e.target.value) || 0\n                                                            }),\n                                                        placeholder: \"0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 668,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 666,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 652,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_8__.Switch, {\n                                                id: \"edit-inStock\",\n                                                checked: formData.inStock,\n                                                onCheckedChange: (checked)=>setFormData({\n                                                        ...formData,\n                                                        inStock: checked\n                                                    })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 678,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                htmlFor: \"edit-inStock\",\n                                                children: \"In Stock\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 683,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 677,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                lineNumber: 559,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_13__.DialogFooter, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: handleUpdate,\n                                    disabled: updateMutation.isPending || !formData.name.trim() || !formData.categoryId,\n                                    children: updateMutation.isPending ? \"Updating...\" : \"Update Product\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                    lineNumber: 687,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                lineNumber: 686,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                        lineNumber: 552,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                    lineNumber: 551,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n            lineNumber: 194,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n        lineNumber: 193,\n        columnNumber: 5\n    }, this);\n}\n_s(ProductsPage, \"5NELiovkC59fvMQvs2NF5F9zK9U=\", false, function() {\n    return [\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_19__.useQueryClient,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_20__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_20__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_21__.useMutation,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_21__.useMutation,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_21__.useMutation\n    ];\n});\n_c = ProductsPage;\nvar _c;\n$RefreshReg$(_c, \"ProductsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/products/page.tsx\n"));

/***/ })

});