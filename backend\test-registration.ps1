# Test script to verify user registration without email verification
# This script tests the registration endpoint to ensure email verification is disabled

Write-Host "Testing GrocEase User Registration (Email Verification Disabled)" -ForegroundColor Green
Write-Host "================================================================" -ForegroundColor Green

# Test data
$timestamp = [DateTimeOffset]::Now.ToUnixTimeSeconds()
$email = "test$<EMAIL>"
$name = "Test User"
$phone = "+1234567890$($timestamp.ToString().Substring($timestamp.ToString().Length - 3))"
$password = "testPassword123"

Write-Host "Testing user registration..." -ForegroundColor Yellow
Write-Host "Email: $email"
Write-Host "Name: $name"
Write-Host "Phone: $phone"

# Registration request body
$body = @{
    name = $name
    email = $email
    phone = $phone
    password = $password
    confirmPassword = $password
} | ConvertTo-Json

try {
    # Register user
    $response = Invoke-RestMethod -Uri "http://localhost:8080/api/auth/register" -Method POST -Body $body -ContentType "application/json"
    
    Write-Host "`nRegistration Response:" -ForegroundColor Cyan
    $response | ConvertTo-Json -Depth 10
    
    if ($response.data.token) {
        Write-Host "`n✅ Registration successful! Token received." -ForegroundColor Green
        Write-Host "✅ Email verification is disabled - user can register without email verification." -ForegroundColor Green
        
        # Test accessing protected endpoint
        Write-Host "`nTesting access to protected endpoint with received token..." -ForegroundColor Yellow
        
        $headers = @{
            "Authorization" = "Bearer $($response.data.token)"
        }
        
        try {
            $profileResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/users/profile" -Method GET -Headers $headers
            
            Write-Host "Profile Response:" -ForegroundColor Cyan
            $profileResponse | ConvertTo-Json -Depth 10
            
            if ($profileResponse.data.isEmailVerified -eq $true) {
                Write-Host "`n✅ SUCCESS: Email verification is disabled!" -ForegroundColor Green
                Write-Host "✅ User profile shows isEmailVerified: true by default" -ForegroundColor Green
                Write-Host "✅ User can access protected endpoints immediately after registration" -ForegroundColor Green
            } else {
                Write-Host "`n❌ ISSUE: Email verification might still be required" -ForegroundColor Red
                Write-Host "❌ User profile shows isEmailVerified: $($profileResponse.data.isEmailVerified)" -ForegroundColor Red
            }
        } catch {
            Write-Host "`n❌ Failed to access protected endpoint: $($_.Exception.Message)" -ForegroundColor Red
        }
        
    } else {
        Write-Host "`n❌ Registration failed or no token received" -ForegroundColor Red
    }
    
} catch {
    Write-Host "`n❌ Registration request failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "❌ Please check if the application is running on http://localhost:8080" -ForegroundColor Red
}

Write-Host "`nTest completed." -ForegroundColor Green
