package com.grocease.dto.banner;

import jakarta.validation.constraints.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CreateBannerRequest {
    
    @NotBlank(message = "Banner title is required")
    @Size(max = 255, message = "Title must not exceed 255 characters")
    private String title;
    
    @Size(max = 500, message = "Subtitle must not exceed 500 characters")
    private String subtitle;
    
    @NotBlank(message = "Banner image is required")
    private String image;
    
    @Pattern(regexp = "^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$", message = "Background color must be a valid hex color code")
    private String backgroundColor;
    
    @Pattern(regexp = "^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$", message = "Text color must be a valid hex color code")
    private String textColor;
    
    @Size(max = 100, message = "Action text must not exceed 100 characters")
    private String actionText;
    
    @Size(max = 500, message = "Action URL must not exceed 500 characters")
    private String actionUrl;
    
    @NotNull(message = "Active status is required")
    private Boolean isActive;
    
    @Min(value = 0, message = "Sort order must be 0 or greater")
    private Integer sortOrder;
}
