package com.grocease.controller;

import com.grocease.dto.ApiResponse;
import com.grocease.dto.PaginatedResponse;
import com.grocease.dto.product.CategoryDto;
import com.grocease.dto.product.CreateCategoryRequest;
import com.grocease.dto.product.CreateProductRequest;
import com.grocease.dto.product.ProductDto;
import com.grocease.service.ProductService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/admin/products")
@RequiredArgsConstructor
@Slf4j
@PreAuthorize("hasRole('ADMIN')")
public class AdminProductController {

    private final ProductService productService;

    @GetMapping
    public ResponseEntity<PaginatedResponse<ProductDto>> getProducts(
            @RequestParam(required = false) Long categoryId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int limit,
            @RequestParam(required = false) String search,
            @RequestParam(defaultValue = "name") String sortBy,
            @RequestParam(defaultValue = "asc") String sortDir) {
        
        log.info("Admin getting products - categoryId: {}, page: {}, limit: {}, search: {}", 
                categoryId, page, limit, search);
        
        PaginatedResponse<ProductDto> response = productService.getProducts(
                categoryId, page, limit, search, sortBy, sortDir);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/{productId}")
    public ResponseEntity<ApiResponse<ProductDto>> getProduct(@PathVariable Long productId) {
        log.info("Admin getting product by id: {}", productId);
        ProductDto product = productService.getProductById(productId);
        return ResponseEntity.ok(ApiResponse.success(product, "Product retrieved successfully"));
    }

    @PostMapping
    public ResponseEntity<ApiResponse<ProductDto>> createProduct(@Valid @RequestBody CreateProductRequest request) {
        log.info("Admin creating product: {}", request.getName());
        ProductDto product = productService.createProduct(request);
        return ResponseEntity.ok(ApiResponse.success(product, "Product created successfully"));
    }

    @PutMapping("/{productId}")
    public ResponseEntity<ApiResponse<ProductDto>> updateProduct(
            @PathVariable Long productId,
            @Valid @RequestBody CreateProductRequest request) {
        log.info("Admin updating product: {}", productId);
        ProductDto product = productService.updateProduct(productId, request);
        return ResponseEntity.ok(ApiResponse.success(product, "Product updated successfully"));
    }

    @DeleteMapping("/{productId}")
    public ResponseEntity<ApiResponse<String>> deleteProduct(@PathVariable Long productId) {
        log.info("Admin deleting product: {}", productId);
        productService.deleteProduct(productId);
        return ResponseEntity.ok(ApiResponse.success("Product deleted successfully", "Product has been removed"));
    }

    // Category management endpoints
    @GetMapping("/categories")
    public ResponseEntity<ApiResponse<List<CategoryDto>>> getCategories() {
        log.info("Admin getting all categories");
        List<CategoryDto> categories = productService.getAllCategoriesForAdmin();
        return ResponseEntity.ok(ApiResponse.success(categories, "Categories retrieved successfully"));
    }

    @GetMapping("/categories/{categoryId}")
    public ResponseEntity<ApiResponse<CategoryDto>> getCategory(@PathVariable Long categoryId) {
        log.info("Admin getting category by id: {}", categoryId);
        CategoryDto category = productService.getCategoryById(categoryId);
        return ResponseEntity.ok(ApiResponse.success(category, "Category retrieved successfully"));
    }

    @PostMapping("/categories")
    public ResponseEntity<ApiResponse<CategoryDto>> createCategory(@Valid @RequestBody CreateCategoryRequest request) {
        log.info("Admin creating category: {}", request.getName());
        CategoryDto category = productService.createCategory(request);
        return ResponseEntity.ok(ApiResponse.success(category, "Category created successfully"));
    }

    @PutMapping("/categories/{categoryId}")
    public ResponseEntity<ApiResponse<CategoryDto>> updateCategory(
            @PathVariable Long categoryId,
            @Valid @RequestBody CreateCategoryRequest request) {
        log.info("Admin updating category: {}", categoryId);
        CategoryDto category = productService.updateCategory(categoryId, request);
        return ResponseEntity.ok(ApiResponse.success(category, "Category updated successfully"));
    }

    @DeleteMapping("/categories/{categoryId}")
    public ResponseEntity<ApiResponse<String>> deleteCategory(@PathVariable Long categoryId) {
        log.info("Admin deleting category: {}", categoryId);
        productService.deleteCategory(categoryId);
        return ResponseEntity.ok(ApiResponse.success("Category deleted successfully", "Category has been removed"));
    }
}
