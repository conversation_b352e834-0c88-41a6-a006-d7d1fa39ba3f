import java.util.Base64;
import java.security.SecureRandom;

public class TestJwtConfig {
    public static void main(String[] args) {
        System.out.println("Testing JWT Configuration...");
        
        // Test the current JWT secret
        String jwtSecret = "GrocEase2024SecureJwtSecretKeyForDevelopmentAndProductionUse123456789";
        System.out.println("JWT Secret length: " + jwtSecret.length() + " characters");
        System.out.println("JWT Secret bytes: " + jwtSecret.getBytes().length + " bytes");
        System.out.println("Required minimum: 32 bytes (256 bits)");
        
        if (jwtSecret.getBytes().length >= 32) {
            System.out.println("✅ JWT Secret is valid!");
        } else {
            System.out.println("❌ JWT Secret is too short!");
        }
        
        // Generate a new secure JWT secret
        System.out.println("\nGenerating a new secure JWT secret...");
        SecureRandom random = new SecureRandom();
        byte[] bytes = new byte[64]; // 512 bits
        random.nextBytes(bytes);
        String newSecret = Base64.getEncoder().encodeToString(bytes);
        System.out.println("New JWT Secret: " + newSecret);
        System.out.println("New JWT Secret length: " + newSecret.length() + " characters");
        
        // Test email verification flag
        String emailVerificationEnabled = System.getProperty("EMAIL_VERIFICATION_ENABLED", "false");
        System.out.println("\nEmail Verification Enabled: " + emailVerificationEnabled);
        
        System.out.println("\n✅ Configuration test completed!");
    }
}
