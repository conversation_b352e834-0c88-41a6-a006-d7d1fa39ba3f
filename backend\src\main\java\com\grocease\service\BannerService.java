package com.grocease.service;

import com.grocease.dto.banner.BannerDto;
import com.grocease.dto.banner.CreateBannerRequest;
import com.grocease.entity.Banner;
import com.grocease.exception.ResourceNotFoundException;
import com.grocease.repository.BannerRepository;
import com.grocease.util.DtoMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
public class BannerService {

    private final BannerRepository bannerRepository;
    private final DtoMapper dtoMapper;

    public List<BannerDto> getAllActiveBanners() {
        List<Banner> banners = bannerRepository.findByIsActiveTrueOrderBySortOrderAsc();
        return dtoMapper.toBannerDtoList(banners);
    }

    // Admin methods
    public List<BannerDto> getAllBannersForAdmin() {
        List<Banner> banners = bannerRepository.findAll(Sort.by(Sort.Direction.ASC, "sortOrder"));
        return dtoMapper.toBannerDtoList(banners);
    }

    public BannerDto getBannerById(Long bannerId) {
        Banner banner = bannerRepository.findById(bannerId)
                .orElseThrow(() -> new ResourceNotFoundException("Banner not found with id: " + bannerId));
        return dtoMapper.toBannerDto(banner);
    }

    @Transactional
    public BannerDto createBanner(CreateBannerRequest request) {
        Banner banner = Banner.builder()
                .title(request.getTitle())
                .subtitle(request.getSubtitle())
                .image(request.getImage())
                .backgroundColor(request.getBackgroundColor())
                .textColor(request.getTextColor())
                .actionText(request.getActionText())
                .actionUrl(request.getActionUrl())
                .isActive(request.getIsActive())
                .sortOrder(request.getSortOrder())
                .build();

        Banner savedBanner = bannerRepository.save(banner);
        return dtoMapper.toBannerDto(savedBanner);
    }

    @Transactional
    public BannerDto updateBanner(Long bannerId, CreateBannerRequest request) {
        Banner banner = bannerRepository.findById(bannerId)
                .orElseThrow(() -> new ResourceNotFoundException("Banner not found with id: " + bannerId));

        banner.setTitle(request.getTitle());
        banner.setSubtitle(request.getSubtitle());
        banner.setImage(request.getImage());
        banner.setBackgroundColor(request.getBackgroundColor());
        banner.setTextColor(request.getTextColor());
        banner.setActionText(request.getActionText());
        banner.setActionUrl(request.getActionUrl());
        banner.setIsActive(request.getIsActive());
        banner.setSortOrder(request.getSortOrder());

        Banner savedBanner = bannerRepository.save(banner);
        return dtoMapper.toBannerDto(savedBanner);
    }

    @Transactional
    public void deleteBanner(Long bannerId) {
        Banner banner = bannerRepository.findById(bannerId)
                .orElseThrow(() -> new ResourceNotFoundException("Banner not found with id: " + bannerId));
        bannerRepository.delete(banner);
    }

    @Transactional
    public BannerDto toggleBannerStatus(Long bannerId) {
        Banner banner = bannerRepository.findById(bannerId)
                .orElseThrow(() -> new ResourceNotFoundException("Banner not found with id: " + bannerId));

        banner.setIsActive(!banner.getIsActive());
        Banner savedBanner = bannerRepository.save(banner);
        return dtoMapper.toBannerDto(savedBanner);
    }
}
