import axios, { AxiosInstance, AxiosResponse } from 'axios'
import { 
  ApiResponse, 
  PaginatedResponse, 
  LoginRequest, 
  AuthResponse,
  DashboardOverview,
  SalesData,
  ProductSales,
  CategorySales,
  UserEngagement,
  Order,
  OrderStatus,
  User,
  Product,
  Category,
  Banner,
  NotificationHistory,
  SendNotificationRequest
} from '@/types'

class ApiClient {
  private client: AxiosInstance

  constructor() {
    this.client = axios.create({
      baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080/api',
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    })

    // Request interceptor to add auth token
    this.client.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('admin_token')
        if (token) {
          config.headers.Authorization = `Bearer ${token}`
        }
        return config
      },
      (error) => Promise.reject(error)
    )

    // Response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          localStorage.removeItem('admin_token')
          localStorage.removeItem('admin_user')
          window.location.href = '/login'
        }
        return Promise.reject(error)
      }
    )
  }

  // Auth endpoints
  async login(credentials: LoginRequest): Promise<AuthResponse> {
    const response: AxiosResponse<ApiResponse<AuthResponse>> = await this.client.post('/auth/login', credentials)
    return response.data.data
  }

  // Dashboard endpoints
  async getDashboardOverview(): Promise<DashboardOverview> {
    const response: AxiosResponse<ApiResponse<DashboardOverview>> = await this.client.get('/admin/dashboard/overview')
    return response.data.data
  }

  // Analytics endpoints
  async getMonthlySalesData(months: number = 12): Promise<SalesData[]> {
    const response: AxiosResponse<ApiResponse<SalesData[]>> = await this.client.get(`/analytics/sales/monthly?months=${months}`)
    return response.data.data
  }

  async getWeeklySalesData(weeks: number = 12): Promise<SalesData[]> {
    const response: AxiosResponse<ApiResponse<SalesData[]>> = await this.client.get(`/analytics/sales/weekly?weeks=${weeks}`)
    return response.data.data
  }

  async getMostSoldProducts(days: number = 30, limit: number = 10): Promise<ProductSales[]> {
    const response: AxiosResponse<ApiResponse<ProductSales[]>> = await this.client.get(`/analytics/products/most-sold?days=${days}&limit=${limit}`)
    return response.data.data
  }

  async getPopularCategories(days: number = 30): Promise<CategorySales[]> {
    const response: AxiosResponse<ApiResponse<CategorySales[]>> = await this.client.get(`/analytics/categories/popular?days=${days}`)
    return response.data.data
  }

  async getUserEngagementMetrics(days: number = 30): Promise<UserEngagement[]> {
    const response: AxiosResponse<ApiResponse<UserEngagement[]>> = await this.client.get(`/analytics/users/engagement?days=${days}`)
    return response.data.data
  }

  // Order endpoints
  async getOrders(page: number = 0, limit: number = 10): Promise<PaginatedResponse<Order>> {
    const response: AxiosResponse<PaginatedResponse<Order>> = await this.client.get(`/orders?page=${page}&limit=${limit}`)
    return response.data
  }

  async getOrder(id: string): Promise<Order> {
    const response: AxiosResponse<ApiResponse<Order>> = await this.client.get(`/orders/${id}`)
    return response.data.data
  }

  async updateOrderStatus(id: string, status: OrderStatus, notes?: string): Promise<Order> {
    const response: AxiosResponse<ApiResponse<Order>> = await this.client.put(`/admin/orders/${id}/status`, { status, notes })
    return response.data.data
  }

  // User endpoints
  async getUsers(page: number = 0, limit: number = 10): Promise<PaginatedResponse<User>> {
    const response: AxiosResponse<PaginatedResponse<User>> = await this.client.get(`/admin/users?page=${page}&limit=${limit}`)
    return response.data
  }

  async getUser(id: string): Promise<User> {
    const response: AxiosResponse<ApiResponse<User>> = await this.client.get(`/admin/users/${id}`)
    return response.data.data
  }

  // Product endpoints
  async getProducts(page: number = 0, limit: number = 10, categoryId?: string, search?: string): Promise<PaginatedResponse<Product>> {
    let url = `/products?page=${page}&limit=${limit}`
    if (categoryId) url += `&categoryId=${categoryId}`
    if (search) url += `&search=${search}`
    
    const response: AxiosResponse<PaginatedResponse<Product>> = await this.client.get(url)
    return response.data
  }

  async getProduct(id: string): Promise<Product> {
    const response: AxiosResponse<ApiResponse<Product>> = await this.client.get(`/products/${id}`)
    return response.data.data
  }

  async getCategories(): Promise<Category[]> {
    const response: AxiosResponse<ApiResponse<Category[]>> = await this.client.get('/categories')
    return response.data.data
  }

  async getBanners(): Promise<Banner[]> {
    const response: AxiosResponse<ApiResponse<Banner[]>> = await this.client.get('/banners')
    return response.data.data
  }

  // Notification endpoints
  async sendNotification(request: SendNotificationRequest): Promise<void> {
    await this.client.post('/admin/notifications/send', request)
  }

  async getNotificationHistory(page: number = 0, size: number = 20, userId?: string): Promise<PaginatedResponse<NotificationHistory>> {
    let url = `/admin/notifications/history?page=${page}&size=${size}`
    if (userId) url += `&userId=${userId}`
    
    const response: AxiosResponse<PaginatedResponse<NotificationHistory>> = await this.client.get(url)
    return response.data
  }

  // File upload endpoints
  async uploadImage(file: File, type: 'avatar' | 'product' | 'category' | 'banner'): Promise<{ imageUrl: string }> {
    const formData = new FormData()
    formData.append('file', file)

    const response: AxiosResponse<ApiResponse<{ imageUrl: string }>> = await this.client.post(`/upload/${type}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
    return response.data.data
  }

  // Admin Product Management
  async createProduct(productData: CreateProductRequest): Promise<Product> {
    const response: AxiosResponse<ApiResponse<Product>> = await this.client.post('/admin/products', productData)
    return response.data.data
  }

  async updateProduct(id: string, productData: CreateProductRequest): Promise<Product> {
    const response: AxiosResponse<ApiResponse<Product>> = await this.client.put(`/admin/products/${id}`, productData)
    return response.data.data
  }

  async deleteProduct(id: string): Promise<void> {
    await this.client.delete(`/admin/products/${id}`)
  }

  // Admin Category Management
  async getAdminCategories(): Promise<Category[]> {
    const response: AxiosResponse<ApiResponse<Category[]>> = await this.client.get('/admin/products/categories')
    return response.data.data
  }

  async createCategory(categoryData: CreateCategoryRequest): Promise<Category> {
    const response: AxiosResponse<ApiResponse<Category>> = await this.client.post('/admin/products/categories', categoryData)
    return response.data.data
  }

  async updateCategory(id: string, categoryData: CreateCategoryRequest): Promise<Category> {
    const response: AxiosResponse<ApiResponse<Category>> = await this.client.put(`/admin/products/categories/${id}`, categoryData)
    return response.data.data
  }

  async deleteCategory(id: string): Promise<void> {
    await this.client.delete(`/admin/products/categories/${id}`)
  }

  // Admin Banner Management
  async getBanners(): Promise<Banner[]> {
    const response: AxiosResponse<ApiResponse<Banner[]>> = await this.client.get('/admin/banners')
    return response.data.data
  }

  async getBanner(id: string): Promise<Banner> {
    const response: AxiosResponse<ApiResponse<Banner>> = await this.client.get(`/admin/banners/${id}`)
    return response.data.data
  }

  async createBanner(bannerData: CreateBannerRequest): Promise<Banner> {
    const response: AxiosResponse<ApiResponse<Banner>> = await this.client.post('/admin/banners', bannerData)
    return response.data.data
  }

  async updateBanner(id: string, bannerData: CreateBannerRequest): Promise<Banner> {
    const response: AxiosResponse<ApiResponse<Banner>> = await this.client.put(`/admin/banners/${id}`, bannerData)
    return response.data.data
  }

  async deleteBanner(id: string): Promise<void> {
    await this.client.delete(`/admin/banners/${id}`)
  }

  async toggleBannerStatus(id: string): Promise<Banner> {
    const response: AxiosResponse<ApiResponse<Banner>> = await this.client.put(`/admin/banners/${id}/toggle-status`)
    return response.data.data
  }

  // Admin User Management
  async getAdminUsers(page: number = 0, limit: number = 10, search?: string): Promise<PaginatedResponse<User>> {
    let url = `/admin/users?page=${page}&limit=${limit}`
    if (search) url += `&search=${search}`

    const response: AxiosResponse<PaginatedResponse<User>> = await this.client.get(url)
    return response.data
  }

  async toggleUserStatus(id: string): Promise<User> {
    const response: AxiosResponse<ApiResponse<User>> = await this.client.put(`/admin/users/${id}/toggle-status`)
    return response.data.data
  }

  async verifyUserEmail(id: string): Promise<User> {
    const response: AxiosResponse<ApiResponse<User>> = await this.client.put(`/admin/users/${id}/verify-email`)
    return response.data.data
  }

  async verifyUserPhone(id: string): Promise<User> {
    const response: AxiosResponse<ApiResponse<User>> = await this.client.put(`/admin/users/${id}/verify-phone`)
    return response.data.data
  }

  async deleteUser(id: string): Promise<void> {
    await this.client.delete(`/admin/users/${id}`)
  }
}

export const apiClient = new ApiClient()
export default apiClient
