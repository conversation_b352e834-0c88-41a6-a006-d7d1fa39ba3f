# 🚀 GrocEase Backend - Complete Setup Guide

## ✅ What We've Simplified

✅ **Single Configuration File**: Removed multiple `application-*.yml` files
✅ **Removed Rate Limiting**: Eliminated problematic bucket4j dependency completely
✅ **Production-Ready**: Optimized for PostgreSQL with environment variables
✅ **No More Confusion**: One configuration file to rule them all!

## 📋 Prerequisites

Before starting, ensure you have:

- ☑️ **Java 17 or higher** - [Download here](https://adoptium.net/)
- ☑️ **Maven 3.6 or higher** - [Download here](https://maven.apache.org/download.cgi)
- ☑️ **PostgreSQL 12 or higher** - [Download here](https://www.postgresql.org/download/)

## 🔧 Quick Setup (5 Minutes)

### Step 1: Copy Environment File
```bash
cd backend
cp .env.example .env
```

### Step 2: Edit Environment Variables
Open `.env` file and fill in these **REQUIRED** values:

```bash
# Database (REQUIRED)
DB_USERNAME=your_db_username
DB_PASSWORD=your_db_password

# JWT Security (REQUIRED)
JWT_SECRET=your_super_secret_jwt_key_at_least_32_characters_long

# Email (REQUIRED for OTP)
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password

# Cloudinary (REQUIRED for image uploads)
CLOUDINARY_CLOUD_NAME=your-cloud-name
CLOUDINARY_API_KEY=your-api-key
CLOUDINARY_API_SECRET=your-api-secret
```

### Step 3: Setup PostgreSQL Database
```sql
-- Connect to PostgreSQL as superuser
CREATE DATABASE grocease_db;
CREATE USER grocease_user WITH PASSWORD 'your_db_password';
GRANT ALL PRIVILEGES ON DATABASE grocease_db TO grocease_user;
```

### Step 4: Build and Run
```bash
# Clean and install dependencies
mvn clean install

# Run the application
mvn spring-boot:run
```

## 🌍 Environment Variables Guide

### 🔒 Required Variables (Must Set)

| Variable | Description | Example |
|----------|-------------|---------|
| `DB_USERNAME` | PostgreSQL username | `grocease_user` |
| `DB_PASSWORD` | PostgreSQL password | `your_secure_password` |
| `JWT_SECRET` | JWT signing key (32+ chars) | `your_super_secret_key...` |
| `MAIL_USERNAME` | SMTP email address | `<EMAIL>` |
| `MAIL_PASSWORD` | SMTP app password | `your-app-password` |
| `CLOUDINARY_CLOUD_NAME` | Cloudinary cloud name | `your-cloud-name` |
| `CLOUDINARY_API_KEY` | Cloudinary API key | `123456789012345` |
| `CLOUDINARY_API_SECRET` | Cloudinary API secret | `your-api-secret` |

### ⚙️ Optional Variables (Have Defaults)

| Variable | Default | Description |
|----------|---------|-------------|
| `SERVER_PORT` | `8080` | Server port |
| `DATABASE_URL` | `********************************************` | Database URL |
| `JPA_DDL_AUTO` | `validate` | Hibernate DDL mode |
| `JPA_SHOW_SQL` | `false` | Show SQL queries |
| `LOG_LEVEL_APP` | `INFO` | Application log level |

## 🔐 Security Setup

### Generate Strong JWT Secret
```bash
# Option 1: Using OpenSSL
openssl rand -base64 32

# Option 2: Using PowerShell
[System.Web.Security.Membership]::GeneratePassword(32, 0)

# Option 3: Online generator
# Visit: https://generate-random.org/api-key-generator
```

### Gmail App Password Setup
1. Enable 2-Factor Authentication on Gmail
2. Go to Google Account Settings → Security → App passwords
3. Generate app password for "Mail"
4. Use this password in `MAIL_PASSWORD`

## 🗄️ Database Modes

### Development Mode (Flexible Schema)
```bash
JPA_DDL_AUTO=update
JPA_SHOW_SQL=true
LOG_LEVEL_APP=DEBUG
```

### Production Mode (Strict Schema)
```bash
JPA_DDL_AUTO=validate
JPA_SHOW_SQL=false
LOG_LEVEL_APP=INFO
```

## 🚀 Running the Application

### Local Development
```bash
mvn spring-boot:run
```

### With Custom Environment
```bash
# Windows
set SPRING_PROFILES_ACTIVE=dev && mvn spring-boot:run

# Linux/Mac
SPRING_PROFILES_ACTIVE=dev mvn spring-boot:run
```

### Production Build
```bash
mvn clean package -DskipTests
java -jar target/grocease-backend-*.jar
```

## 🔍 Verification

### Check Application Health
```bash
curl http://localhost:8080/api/actuator/health
```

Expected response:
```json
{"status":"UP"}
```

### Check Database Connection
Look for this in logs:
```
HikariPool-1 - Start completed.
```

## 🐛 Troubleshooting

### Common Issues

**1. Maven Dependency Error**
```
✅ FIXED: Completely removed problematic bucket4j dependency
REMOVED: All rate limiting functionality using bucket4j
RESULT: No more compilation errors!
```

**2. Multiple Configuration Files**
```
✅ FIXED: Removed all profile-specific files, using single application.yml
```

**3. Database Connection Failed**
```bash
# Check PostgreSQL is running
pg_ctl status

# Check database exists
psql -U postgres -c "\l"

# Verify user permissions
psql -U postgres -c "\du"
```

**4. JWT Secret Too Short**
```
Error: JWT secret must be at least 32 characters
Solution: Generate longer secret using methods above
```

## 📁 Project Structure (Simplified)

```
backend/
├── src/main/resources/
│   ├── application.yml          # ✅ SINGLE config file
│   └── firebase-service-account.json (optional)
├── .env.example                 # ✅ Environment template
├── .env                        # ✅ Your actual values (create this)
├── pom.xml                     # ✅ Fixed dependencies
└── SETUP_GUIDE.md              # ✅ This guide
```

## 🎉 Success!

If you see this in your logs, you're ready to go:
```
Started GrocEaseApplication in X.XXX seconds
```

Your API will be available at: `http://localhost:8080/api`

## 📞 Need Help?

If you encounter any issues:
1. Check the logs in `logs/grocease-backend.log`
2. Verify all required environment variables are set
3. Ensure PostgreSQL is running and accessible
4. Check firewall settings for port 8080
