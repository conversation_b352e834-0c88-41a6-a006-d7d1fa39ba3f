"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/settings/page",{

/***/ "(app-pages-browser)/./src/app/settings/page.tsx":
/*!***********************************!*\
  !*** ./src/app/settings/page.tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SettingsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _store_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/auth */ \"(app-pages-browser)/./src/store/auth.ts\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useMutation.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _components_layout_AdminLayout__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/layout/AdminLayout */ \"(app-pages-browser)/./src/components/layout/AdminLayout.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_switch__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/switch */ \"(app-pages-browser)/./src/components/ui/switch.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./src/components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_image_upload__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/image-upload */ \"(app-pages-browser)/./src/components/ui/image-upload.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bell_Database_Save_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Database,Save,Shield,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Database_Save_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Database,Save,Shield,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Database_Save_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Database,Save,Shield,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Database_Save_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Database,Save,Shield,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Database_Save_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Database,Save,Shield,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction SettingsPage() {\n    _s();\n    const { user, updateUser } = (0,_store_auth__WEBPACK_IMPORTED_MODULE_2__.useAuthStore)();\n    const [profileData, setProfileData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: (user === null || user === void 0 ? void 0 : user.name) || \"\",\n        email: (user === null || user === void 0 ? void 0 : user.email) || \"\",\n        phone: (user === null || user === void 0 ? void 0 : user.phone) || \"\",\n        avatar: (user === null || user === void 0 ? void 0 : user.avatar) || \"\"\n    });\n    const [notificationSettings, setNotificationSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        emailNotifications: true,\n        pushNotifications: true,\n        orderUpdates: true,\n        marketingEmails: false,\n        weeklyReports: true\n    });\n    const [systemSettings, setSystemSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        maintenanceMode: false,\n        allowRegistrations: true,\n        requireEmailVerification: true,\n        enableAnalytics: true,\n        autoBackup: true\n    });\n    const updateProfileMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_15__.useMutation)({\n        mutationFn: (data)=>_lib_api__WEBPACK_IMPORTED_MODULE_3__[\"default\"].updateProfile(data),\n        onSuccess: (updatedUser)=>{\n            updateUser(updatedUser);\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Profile updated successfully\");\n        },\n        onError: (error)=>{\n            var _error_response_data, _error_response;\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"Failed to update profile\");\n        }\n    });\n    const handleProfileSave = ()=>{\n        updateProfileMutation.mutate(profileData);\n    };\n    const handleNotificationSave = ()=>{\n        // Implementation for saving notification settings\n        console.log(\"Saving notification settings:\", notificationSettings);\n    };\n    const handleSystemSave = ()=>{\n        // Implementation for saving system settings\n        console.log(\"Saving system settings:\", systemSettings);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_AdminLayout__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold tracking-tight\",\n                            children: \"Settings\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground\",\n                            children: \"Manage your account and system preferences\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 80,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.Tabs, {\n                    defaultValue: \"profile\",\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.TabsList, {\n                            className: \"grid w-full grid-cols-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.TabsTrigger, {\n                                    value: \"profile\",\n                                    children: \"Profile\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.TabsTrigger, {\n                                    value: \"notifications\",\n                                    children: \"Notifications\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.TabsTrigger, {\n                                    value: \"security\",\n                                    children: \"Security\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.TabsTrigger, {\n                                    value: \"system\",\n                                    children: \"System\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.TabsContent, {\n                            value: \"profile\",\n                            className: \"space-y-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Database_Save_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"h-5 w-5 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 100,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Profile Information\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 99,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardDescription, {\n                                                children: \"Update your personal information and profile picture\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 103,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_9__.Label, {\n                                                        className: \"text-base font-medium\",\n                                                        children: \"Profile Picture\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 109,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-muted-foreground mb-4\",\n                                                        children: \"Upload a new avatar or profile picture\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 110,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_image_upload__WEBPACK_IMPORTED_MODULE_13__.ImageUpload, {\n                                                        value: profileData.avatar,\n                                                        onChange: (url)=>setProfileData({\n                                                                ...profileData,\n                                                                avatar: url\n                                                            }),\n                                                        type: \"avatar\",\n                                                        preview: \"avatar\",\n                                                        placeholder: \"Upload your profile picture\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 113,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_10__.Separator, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid gap-4 md:grid-cols-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_9__.Label, {\n                                                                htmlFor: \"name\",\n                                                                children: \"Full Name\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 126,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                                                id: \"name\",\n                                                                value: profileData.name,\n                                                                onChange: (e)=>setProfileData({\n                                                                        ...profileData,\n                                                                        name: e.target.value\n                                                                    })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 127,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 125,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_9__.Label, {\n                                                                htmlFor: \"email\",\n                                                                children: \"Email\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 134,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                                                id: \"email\",\n                                                                type: \"email\",\n                                                                value: profileData.email,\n                                                                onChange: (e)=>setProfileData({\n                                                                        ...profileData,\n                                                                        email: e.target.value\n                                                                    })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 135,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 133,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_9__.Label, {\n                                                                htmlFor: \"phone\",\n                                                                children: \"Phone\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 143,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                                                id: \"phone\",\n                                                                value: profileData.phone,\n                                                                onChange: (e)=>setProfileData({\n                                                                        ...profileData,\n                                                                        phone: e.target.value\n                                                                    })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 144,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 142,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                onClick: handleProfileSave,\n                                                disabled: updateProfileMutation.isPending,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Database_Save_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 156,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    updateProfileMutation.isPending ? \"Saving...\" : \"Save Changes\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 152,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.TabsContent, {\n                            value: \"notifications\",\n                            className: \"space-y-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Database_Save_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"h-5 w-5 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 168,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Notification Preferences\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 167,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardDescription, {\n                                                children: \"Configure how you want to receive notifications\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-0.5\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_9__.Label, {\n                                                                        children: \"Email Notifications\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 179,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: \"Receive notifications via email\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 180,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 178,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_11__.Switch, {\n                                                                checked: notificationSettings.emailNotifications,\n                                                                onCheckedChange: (checked)=>setNotificationSettings({\n                                                                        ...notificationSettings,\n                                                                        emailNotifications: checked\n                                                                    })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 184,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 177,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-0.5\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_9__.Label, {\n                                                                        children: \"Push Notifications\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 194,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: \"Receive push notifications in browser\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 195,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 193,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_11__.Switch, {\n                                                                checked: notificationSettings.pushNotifications,\n                                                                onCheckedChange: (checked)=>setNotificationSettings({\n                                                                        ...notificationSettings,\n                                                                        pushNotifications: checked\n                                                                    })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 199,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 192,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-0.5\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_9__.Label, {\n                                                                        children: \"Order Updates\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 209,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: \"Get notified about order status changes\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 210,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 208,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_11__.Switch, {\n                                                                checked: notificationSettings.orderUpdates,\n                                                                onCheckedChange: (checked)=>setNotificationSettings({\n                                                                        ...notificationSettings,\n                                                                        orderUpdates: checked\n                                                                    })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 214,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 207,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-0.5\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_9__.Label, {\n                                                                        children: \"Marketing Emails\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 224,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: \"Receive promotional and marketing emails\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 225,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 223,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_11__.Switch, {\n                                                                checked: notificationSettings.marketingEmails,\n                                                                onCheckedChange: (checked)=>setNotificationSettings({\n                                                                        ...notificationSettings,\n                                                                        marketingEmails: checked\n                                                                    })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 229,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 222,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-0.5\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_9__.Label, {\n                                                                        children: \"Weekly Reports\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 239,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: \"Receive weekly analytics reports\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 240,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 238,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_11__.Switch, {\n                                                                checked: notificationSettings.weeklyReports,\n                                                                onCheckedChange: (checked)=>setNotificationSettings({\n                                                                        ...notificationSettings,\n                                                                        weeklyReports: checked\n                                                                    })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 244,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 237,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                onClick: handleNotificationSave,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Database_Save_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 254,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Save Preferences\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 163,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.TabsContent, {\n                            value: \"security\",\n                            className: \"space-y-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Database_Save_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-5 w-5 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Security Settings\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardDescription, {\n                                                children: \"Manage your account security and authentication\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 269,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 264,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium mb-4\",\n                                                        children: \"Change Password\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 275,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_9__.Label, {\n                                                                        htmlFor: \"current-password\",\n                                                                        children: \"Current Password\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 278,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                                                        id: \"current-password\",\n                                                                        type: \"password\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 279,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 277,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_9__.Label, {\n                                                                        htmlFor: \"new-password\",\n                                                                        children: \"New Password\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 282,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                                                        id: \"new-password\",\n                                                                        type: \"password\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 283,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 281,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_9__.Label, {\n                                                                        htmlFor: \"confirm-password\",\n                                                                        children: \"Confirm New Password\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 286,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                                                        id: \"confirm-password\",\n                                                                        type: \"password\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 287,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 285,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                                className: \"w-fit\",\n                                                                children: \"Update Password\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 289,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 276,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 274,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_10__.Separator, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 293,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium mb-4\",\n                                                        children: \"Two-Factor Authentication\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 296,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm\",\n                                                                        children: \"Enable two-factor authentication for enhanced security\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 299,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: \"Currently disabled\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 300,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 298,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                                variant: \"outline\",\n                                                                children: \"Enable 2FA\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 304,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 297,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_10__.Separator, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 308,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium mb-4\",\n                                                        children: \"Active Sessions\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 311,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between p-3 border rounded-lg\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium\",\n                                                                            children: \"Current Session\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 315,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-muted-foreground\",\n                                                                            children: \"Chrome on Windows • Active now\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 316,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 314,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_14__.Badge, {\n                                                                    variant: \"default\",\n                                                                    children: \"Current\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 318,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 313,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 312,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 310,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 263,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 261,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.TabsContent, {\n                            value: \"system\",\n                            className: \"space-y-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Database_Save_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"h-5 w-5 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 331,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"System Configuration\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardDescription, {\n                                                children: \"Configure system-wide settings and preferences\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 329,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-0.5\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_9__.Label, {\n                                                                        children: \"Maintenance Mode\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 342,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: \"Put the system in maintenance mode\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 343,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 341,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_11__.Switch, {\n                                                                checked: systemSettings.maintenanceMode,\n                                                                onCheckedChange: (checked)=>setSystemSettings({\n                                                                        ...systemSettings,\n                                                                        maintenanceMode: checked\n                                                                    })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 347,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 340,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-0.5\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_9__.Label, {\n                                                                        children: \"Allow User Registrations\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 357,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: \"Allow new users to register accounts\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 358,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 356,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_11__.Switch, {\n                                                                checked: systemSettings.allowRegistrations,\n                                                                onCheckedChange: (checked)=>setSystemSettings({\n                                                                        ...systemSettings,\n                                                                        allowRegistrations: checked\n                                                                    })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 362,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 355,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-0.5\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_9__.Label, {\n                                                                        children: \"Require Email Verification\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 372,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: \"Require users to verify their email addresses\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 373,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 371,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_11__.Switch, {\n                                                                checked: systemSettings.requireEmailVerification,\n                                                                onCheckedChange: (checked)=>setSystemSettings({\n                                                                        ...systemSettings,\n                                                                        requireEmailVerification: checked\n                                                                    })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 377,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 370,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-0.5\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_9__.Label, {\n                                                                        children: \"Enable Analytics\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 387,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: \"Collect analytics data for insights\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 388,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 386,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_11__.Switch, {\n                                                                checked: systemSettings.enableAnalytics,\n                                                                onCheckedChange: (checked)=>setSystemSettings({\n                                                                        ...systemSettings,\n                                                                        enableAnalytics: checked\n                                                                    })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 392,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 385,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-0.5\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_9__.Label, {\n                                                                        children: \"Automatic Backups\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 402,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: \"Enable automatic daily database backups\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 403,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 401,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_11__.Switch, {\n                                                                checked: systemSettings.autoBackup,\n                                                                onCheckedChange: (checked)=>setSystemSettings({\n                                                                        ...systemSettings,\n                                                                        autoBackup: checked\n                                                                    })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 407,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 400,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 339,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                onClick: handleSystemSave,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Database_Save_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 417,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Save System Settings\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 416,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 338,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 326,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n            lineNumber: 78,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\grocease\\\\admin-panel\\\\src\\\\app\\\\settings\\\\page.tsx\",\n        lineNumber: 77,\n        columnNumber: 5\n    }, this);\n}\n_s(SettingsPage, \"pygKfhikdIPxXI+x1Jkm5hkcsnI=\", false, function() {\n    return [\n        _store_auth__WEBPACK_IMPORTED_MODULE_2__.useAuthStore,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_15__.useMutation\n    ];\n});\n_c = SettingsPage;\nvar _c;\n$RefreshReg$(_c, \"SettingsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/settings/page.tsx\n"));

/***/ })

});