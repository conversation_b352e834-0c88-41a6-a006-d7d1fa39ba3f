package com.grocease.controller;

import com.grocease.dto.ApiResponse;
import com.grocease.dto.banner.BannerDto;
import com.grocease.dto.banner.CreateBannerRequest;
import com.grocease.service.BannerService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/admin/banners")
@RequiredArgsConstructor
@Slf4j
@PreAuthorize("hasRole('ADMIN')")
public class AdminBannerController {

    private final BannerService bannerService;

    @GetMapping
    public ResponseEntity<ApiResponse<List<BannerDto>>> getAllBanners() {
        log.info("Admin getting all banners");
        List<BannerDto> banners = bannerService.getAllBannersForAdmin();
        return ResponseEntity.ok(ApiResponse.success(banners, "Banners retrieved successfully"));
    }

    @GetMapping("/{bannerId}")
    public ResponseEntity<ApiResponse<BannerDto>> getBanner(@PathVariable Long bannerId) {
        log.info("Admin getting banner by id: {}", bannerId);
        BannerDto banner = bannerService.getBannerById(bannerId);
        return ResponseEntity.ok(ApiResponse.success(banner, "Banner retrieved successfully"));
    }

    @PostMapping
    public ResponseEntity<ApiResponse<BannerDto>> createBanner(@Valid @RequestBody CreateBannerRequest request) {
        log.info("Admin creating banner: {}", request.getTitle());
        BannerDto banner = bannerService.createBanner(request);
        return ResponseEntity.ok(ApiResponse.success(banner, "Banner created successfully"));
    }

    @PutMapping("/{bannerId}")
    public ResponseEntity<ApiResponse<BannerDto>> updateBanner(
            @PathVariable Long bannerId,
            @Valid @RequestBody CreateBannerRequest request) {
        log.info("Admin updating banner: {}", bannerId);
        BannerDto banner = bannerService.updateBanner(bannerId, request);
        return ResponseEntity.ok(ApiResponse.success(banner, "Banner updated successfully"));
    }

    @DeleteMapping("/{bannerId}")
    public ResponseEntity<ApiResponse<String>> deleteBanner(@PathVariable Long bannerId) {
        log.info("Admin deleting banner: {}", bannerId);
        bannerService.deleteBanner(bannerId);
        return ResponseEntity.ok(ApiResponse.success("Banner deleted successfully", "Banner has been removed"));
    }

    @PutMapping("/{bannerId}/toggle-status")
    public ResponseEntity<ApiResponse<BannerDto>> toggleBannerStatus(@PathVariable Long bannerId) {
        log.info("Admin toggling banner status: {}", bannerId);
        BannerDto banner = bannerService.toggleBannerStatus(bannerId);
        return ResponseEntity.ok(ApiResponse.success(banner, "Banner status updated successfully"));
    }
}
