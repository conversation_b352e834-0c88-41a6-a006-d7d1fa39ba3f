@echo off
echo Starting GrocEase Backend Application...
echo =====================================

REM Set environment variables
set JWT_SECRET=GrocEase2024SecureJwtSecretKeyForDevelopmentAndProductionUse123456789
set EMAIL_VERIFICATION_ENABLED=false
set DB_USERNAME=postgres
set DB_PASSWORD=admin
set MAIL_USERNAME=<EMAIL>
set MAIL_PASSWORD=your-app-password
set CLOUDINARY_CLOUD_NAME=your-cloud-name
set CLOUDINARY_API_KEY=your-api-key
set CLOUDINARY_API_SECRET=your-api-secret

echo Environment variables set:
echo - JWT_SECRET: [HIDDEN - %JWT_SECRET:~0,20%...]
echo - EMAIL_VERIFICATION_ENABLED: %EMAIL_VERIFICATION_ENABLED%
echo - DB_USERNAME: %DB_USERNAME%
echo.

REM Check if Maven is available
where mvn >nul 2>nul
if %ERRORLEVEL% EQU 0 (
    echo Maven found, starting application with <PERSON><PERSON>...
    mvn spring-boot:run
) else (
    echo Maven not found, trying alternative methods...
    
    REM Try to find Maven in common locations
    if exist "C:\Program Files\Apache\maven\bin\mvn.cmd" (
        echo Found Maven in Program Files...
        "C:\Program Files\Apache\maven\bin\mvn.cmd" spring-boot:run
    ) else if exist "C:\apache-maven\bin\mvn.cmd" (
        echo Found Maven in C:\apache-maven...
        "C:\apache-maven\bin\mvn.cmd" spring-boot:run
    ) else (
        echo Maven not found in common locations.
        echo Please install Maven or run the application from your IDE.
        echo.
        echo Alternative: You can also run with Java directly if you have a JAR file:
        echo java -jar target\grocease-backend-*.jar
        echo.
        pause
    )
)
