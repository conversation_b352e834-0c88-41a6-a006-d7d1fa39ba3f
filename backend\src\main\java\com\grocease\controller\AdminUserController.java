package com.grocease.controller;

import com.grocease.dto.ApiResponse;
import com.grocease.dto.PaginatedResponse;
import com.grocease.dto.user.UserDto;
import com.grocease.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/admin/users")
@RequiredArgsConstructor
@Slf4j
@PreAuthorize("hasRole('ADMIN')")
public class AdminUserController {

    private final UserService userService;

    @GetMapping
    public ResponseEntity<PaginatedResponse<UserDto>> getUsers(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int limit,
            @RequestParam(required = false) String search,
            @RequestParam(defaultValue = "createdAt") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir) {
        
        log.info("Admin getting users - page: {}, limit: {}, search: {}", page, limit, search);
        
        PaginatedResponse<UserDto> response = userService.getUsers(page, limit, search, sortBy, sortDir);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/{userId}")
    public ResponseEntity<ApiResponse<UserDto>> getUser(@PathVariable Long userId) {
        log.info("Admin getting user by id: {}", userId);
        UserDto user = userService.getUserById(userId);
        return ResponseEntity.ok(ApiResponse.success(user, "User retrieved successfully"));
    }

    @PutMapping("/{userId}/toggle-status")
    public ResponseEntity<ApiResponse<UserDto>> toggleUserStatus(@PathVariable Long userId) {
        log.info("Admin toggling user status: {}", userId);
        UserDto user = userService.toggleUserStatus(userId);
        return ResponseEntity.ok(ApiResponse.success(user, "User status updated successfully"));
    }

    @PutMapping("/{userId}/verify-email")
    public ResponseEntity<ApiResponse<UserDto>> verifyUserEmail(@PathVariable Long userId) {
        log.info("Admin verifying user email: {}", userId);
        UserDto user = userService.verifyUserEmail(userId);
        return ResponseEntity.ok(ApiResponse.success(user, "User email verified successfully"));
    }

    @PutMapping("/{userId}/verify-phone")
    public ResponseEntity<ApiResponse<UserDto>> verifyUserPhone(@PathVariable Long userId) {
        log.info("Admin verifying user phone: {}", userId);
        UserDto user = userService.verifyUserPhone(userId);
        return ResponseEntity.ok(ApiResponse.success(user, "User phone verified successfully"));
    }

    @DeleteMapping("/{userId}")
    public ResponseEntity<ApiResponse<String>> deleteUser(@PathVariable Long userId) {
        log.info("Admin deleting user: {}", userId);
        userService.deleteUser(userId);
        return ResponseEntity.ok(ApiResponse.success("User deleted successfully", "User has been removed"));
    }
}
