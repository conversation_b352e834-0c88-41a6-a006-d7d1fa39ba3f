"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/store/auth.ts":
/*!***************************!*\
  !*** ./src/store/auth.ts ***!
  \***************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuthStore: function() { return /* binding */ useAuthStore; }\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand */ \"(app-pages-browser)/./node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zustand/middleware */ \"(app-pages-browser)/./node_modules/zustand/esm/middleware.mjs\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n\n\n\nconst useAuthStore = (0,zustand__WEBPACK_IMPORTED_MODULE_1__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_2__.persist)((set, get)=>({\n        user: null,\n        token: null,\n        isAuthenticated: false,\n        isLoading: true,\n        hasHydrated: false,\n        login: async (email, password)=>{\n            try {\n                set({\n                    isLoading: true\n                });\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].login({\n                    email,\n                    password\n                });\n                // Store token in localStorage for API client\n                localStorage.setItem(\"admin_token\", response.token);\n                localStorage.setItem(\"admin_user\", JSON.stringify(response.user));\n                set({\n                    user: response.user,\n                    token: response.token,\n                    isAuthenticated: true,\n                    isLoading: false\n                });\n            } catch (error) {\n                set({\n                    isLoading: false\n                });\n                throw error;\n            }\n        },\n        logout: ()=>{\n            localStorage.removeItem(\"admin_token\");\n            localStorage.removeItem(\"admin_user\");\n            set({\n                user: null,\n                token: null,\n                isAuthenticated: false,\n                isLoading: false\n            });\n        },\n        setLoading: (loading)=>{\n            set({\n                isLoading: loading\n            });\n        },\n        initializeAuth: ()=>{\n            try {\n                const token = localStorage.getItem(\"admin_token\");\n                const userStr = localStorage.getItem(\"admin_user\");\n                if (token && userStr) {\n                    const user = JSON.parse(userStr);\n                    set({\n                        user,\n                        token,\n                        isAuthenticated: true,\n                        isLoading: false\n                    });\n                } else {\n                    set({\n                        user: null,\n                        token: null,\n                        isAuthenticated: false,\n                        isLoading: false\n                    });\n                }\n            } catch (error) {\n                console.error(\"Error initializing auth:\", error);\n                set({\n                    user: null,\n                    token: null,\n                    isAuthenticated: false,\n                    isLoading: false\n                });\n            }\n        },\n        setHasHydrated: (hasHydrated)=>{\n            set({\n                hasHydrated\n            });\n        }\n    }), {\n    name: \"admin-auth-storage\",\n    storage: (0,zustand_middleware__WEBPACK_IMPORTED_MODULE_2__.createJSONStorage)(()=>localStorage),\n    partialize: (state)=>({\n            user: state.user,\n            token: state.token,\n            isAuthenticated: state.isAuthenticated\n        }),\n    onRehydrateStorage: ()=>(state)=>{\n            state === null || state === void 0 ? void 0 : state.setHasHydrated(true);\n            state === null || state === void 0 ? void 0 : state.initializeAuth();\n        }\n}));\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/store/auth.ts\n"));

/***/ })

});