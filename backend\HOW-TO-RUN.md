# 🚀 How to Run GrocEase Backend

## ✅ Quick Solutions (Choose One)

### Option 1: Use IntelliJ IDEA (Recommended)
1. **Download IntelliJ IDEA Community** (free): https://www.jetbrains.com/idea/download/
2. **Open Project**: File → Open → Select the `backend` folder
3. **Set Environment Variables**:
   - Run → Edit Configurations
   - Add Environment Variables:
     ```
     JWT_SECRET=GrocEase2024SecureJwtSecretKeyForDevelopmentAndProductionUse123456789
     EMAIL_VERIFICATION_ENABLED=false
     DB_USERNAME=postgres
     DB_PASSWORD=admin
     ```
4. **Run**: Right-click `GrocEaseApplication.java` → Run

### Option 2: Install Maven
1. **Run**: `.\install-maven.bat` (in backend folder)
2. **Then run**: `mvn spring-boot:run`

### Option 3: Use VS Code
1. **Install Java Extension Pack** in VS Code
2. **Open** the `backend` folder in VS Code
3. **Set environment variables** in launch.json
4. **Run** the application

## 🔧 Current Status

✅ **Fixed Issues:**
- JWT secret configuration
- Email verification (disabled for development)
- NullPointerException in DtoMapper
- User entity builder initialization

❌ **Remaining Issue:**
- Need proper build tool (Maven) or IDE to compile and run

## 🎯 What Will Work After Running

Once the application starts, you can:

1. **Register Admin User**:
```bash
curl -X POST http://localhost:8080/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Admin User",
    "email": "<EMAIL>",
    "phone": "+1234567890",
    "password": "admin123",
    "confirmPassword": "admin123"
  }'
```

2. **Expected Response**:
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "1",
      "name": "Admin User",
      "email": "<EMAIL>",
      "isEmailVerified": true
    },
    "token": "eyJhbGciOiJIUzI1NiJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiJ9..."
  }
}
```

## 🚨 Why Compilation Fails

The compilation errors you see are because:
- Spring Boot dependencies aren't in classpath
- Individual Java files can't be compiled without the full project context
- Need Maven or IDE to handle dependencies

**Solution**: Use one of the options above! 🚀
